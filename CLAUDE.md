# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

这是一个安防/监控系统的开发项目，主要用C/C++语言开发，采用分层架构设计。

### 项目架构

- **10-common/**: 公共组件库，包含头文件、库文件和文档
  - `include/`: 系统头文件，包含cbb公共组件、hal硬件抽象层、service服务层等
  - `lib/`: 预编译库文件
  - `docs/`: 文档

- **40-service/**: 核心服务层，包含各种系统服务
  - `nvrcfg`: NVR配置服务
  - `nvrproto`: NVR协议服务
  - `nvrcap`: 视频捕获服务
  - `nvralarm`: 报警服务
  - `common/`: 服务层公共组件

- **41-servicefix/**: 服务修复/补丁层

- **50-app/**: 应用层，包含各种应用程序
  - `appbase`: 应用基础框架
  - `onvifapp`: ONVIF协议应用
  - `cgiapp`: CGI应用
  - `pubsecapp`: 公安应用
  - `rtspapp`: RTSP应用

## 构建系统

### 构建工具
项目使用自定义Makefile构建系统，支持多种目标平台：

**Windows平台**: 使用Visual Studio解决方案文件(.sln)
- VS2008项目: `prj_win32/`目录下
- VS2019项目: `prj_win/vs2019/`目录下

**Linux平台**: 使用Makefile构建系统
- 项目根目录：`prj_linux/`
- 公共构建配置：`40-service/common/common.mk`

### 常用构建命令

#### Windows构建
```bash
# 在相应的Visual Studio目录下
devenv nvrcfg.sln /build Debug
devenv nvrcfg.sln /build Release
```

#### Linux构建
```bash
# 在项目的prj_linux目录下
make                    # 构建项目
make clean             # 清理构建产物
make install           # 安装目标文件
make DEBUG=1           # 调试构建
```

#### 交叉编译
项目支持多种嵌入式平台交叉编译，通过LINUX_COMPILER环境变量控制：
- `_ARM_HIS3536_`: 海思3536平台
- `_SSC339G_`: SigmaStar 339G平台
- `_HIS3516DV300_`: 海思3516DV300平台
- 其他支持的平台见`makelib.mk`文件第120-305行

### 构建配置

**关键环境变量**:
- `LINUX_COMPILER`: 指定目标编译器平台
- `DEBUG`: 设置为1开启调试模式
- `ETI_TOOLKIT`: 工具链基础目录
- `ETI_TOOLKIT_INSTALL`: 安装目录

**构建目标类型**:
- `ARC_TARGET`: 静态库(.a)
- `SO_TARGET`: 动态库(.so)
- `APP_TARGET`: 可执行程序

## 代码结构

### 公共组件库 (10-common/include/cbb/)
包含大量第三方库和自研组件：
- 网络协议：libwebsockets, curl, rtsp, sip
- 视频处理：视频算法组件 (videoalg_*)
- 加密安全：openssl, kshield
- 数据格式：cjson, protobuf, mxml
- 平台支持：各种嵌入式平台的HAL层

### 服务层架构 (40-service/)
采用微服务架构，主要服务包括：
- 配置管理 (`nvrcfg`)
- 协议处理 (`nvrproto`)
- 视频捕获 (`nvrcap`)
- 录像管理 (`nvrrec`)
- 报警处理 (`nvralarm`)
- 网络管理 (`nvrnetwork`)

### 应用层 (50-app/)
- `appbase`: 提供应用程序基础框架
- ONVIF应用: 实现ONVIF协议支持
- Web应用: CGI接口实现
- 公安专用应用: 特定行业应用

## 开发规范

### 编译器配置
项目在VS Code中配置了C/C++插件，关键配置：
- 禁用错误波浪线：`"C_Cpp.errorSquiggles": "disabled"`
- 预定义宏：`"_BASIC_INTELLI_"`, `"_SSC339G_"`

### 调试与测试
- 测试程序位于各模块的`test/`目录下
- 使用shell脚本进行自动化构建和部署
- 公共shell函数定义在`40-service/common/shell_public_function`

## 文件忽略配置

项目配置了`.cursorignore`文件，忽略以下目录：
- `10-common/lib/`: 预编译库文件
- `10-common/docs/`: 文档目录
- 各种视频算法组件库

## 版本控制

项目使用SVN进行版本控制（.svn目录），同时配置了Phabricator代码审查：
- 仓库标识：`"8.1.5"`
- Phabricator地址：`http://10.165.26.189`