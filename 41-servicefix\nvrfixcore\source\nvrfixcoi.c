/**
 * @file nvrfixcoi.c
 * @brief COITRANSDATA
 * <AUTHOR> Code
 * @date 2025-01-01
 * @version 1.0
 * @copyright V1.0 Copyright(C) 2025 NVR All rights reserved.
 */

#include "nvrfixcoi.h"
#include "nvrcoi.h"
#include "nvralarm.h"
#include "nvrsys.h"
#include "nvrdef.h"
#include "coidef.h"
#include "nvrfixcore.h"
#include "cJSON.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>


// 全局变量
static BOOL32 g_bNvrFixCoiInited = FALSE;
static u32 g_dwTransDataMsgCount = 0;
static u32 g_dwTransDataSuccCount = 0;
static u32 g_dwTransDataFailCount = 0;

// JSON格式报警信息结构
typedef struct tagNvrFixCoiJsonAlarmInfo
{
    u32 dwAlarmType;        // 报警类型
    u32 dwAlarmStatus;      // 报警状态 (0:取消报警, 1:触发报警)
    s8 achExtraData[256];   // 扩展数据描述
    u16 wChnId;             // 通道ID (可选，默认为0)
    u16 wAlarmNo;           // 报警编号 (可选，默认为0)
} TNvrFixCoiJsonAlarmInfo;

// 内部函数声明
static NVRSTATUS NvrFixCoiProcessTransData(u8* pData, u32 dwDataLen);
static NVRSTATUS NvrFixCoiProcessJsonData(const s8* pszJsonData, u32 dwDataLen);
static NVRSTATUS NvrFixCoiProcessJsonAlarm(cJSON* pJsonObj);
static NVRSTATUS NvrFixCoiTriggerJsonAlarm(TNvrFixCoiJsonAlarmInfo* ptAlarmInfo);
static BOOL32 NvrFixCoiIsJsonData(const u8* pData, u32 dwDataLen);
static void NvrFixCoiPrintHexData(const s8* pszPrefix, u8* pData, u32 dwDataLen);

/**
 * @brief COI服务层消息处理回调函数
 * @param eMsgType 消息类型
 * @param ptParamIn 输入参数
 * @param ptParamOut 输出参数
 * @param pContext 上下文
 * @return 执行结果
 */
NVRSTATUS NvrFixCoiServiceMsgHandler(EEdgeCOIMsgType eMsgType, TEdgeCoiParam *ptParamIn, TEdgeCoiParam *ptParamOut, void* pContext)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    if(!g_bNvrFixCoiInited)
    {
        PRINTERR("NvrFixCoi not initialized\n");
        return NVR_ERR__ERROR;
    }

    if(!ptParamIn)
    {
        PRINTERR("ptParamIn is NULL\n");
        return NVR_ERR__PARAM_INVALID;
    }

    PRINTDBG("Received COI message: type=%d\n", eMsgType);

    switch(eMsgType)
    {
        case EDGE_COI_TRANSDATA:
        {
            if(ptParamIn->pData && ptParamIn->dwDataLen > 0)
            {
                g_dwTransDataMsgCount++;

                PRINTVIP("Received TRANSDATA from server, len=%u, total=%u\n",
                             ptParamIn->dwDataLen, g_dwTransDataMsgCount);

                // 打印接收到的数据（调试用）
                NvrFixCoiPrintHexData("RX", (u8*)ptParamIn->pData, ptParamIn->dwDataLen);

                // 处理透传数据
                eRet = NvrFixCoiProcessTransData((u8*)ptParamIn->pData, ptParamIn->dwDataLen);
                if(NVR_ERR__OK == eRet)
                {
                    g_dwTransDataSuccCount++;
                    PRINTDBG("Process TRANSDATA success\n");
                }
                else
                {
                    g_dwTransDataFailCount++;
                    PRINTERR("Process TRANSDATA failed: %d\n", eRet);
                }

                // 设置返回状态（可选）
                if(ptParamOut)
                {
                    static u8 s_byResponse[] = {0x00, 0x01}; // 简单的ACK响应
                    ptParamOut->dwType = ptParamIn->dwType;
                    ptParamOut->dwDataLen = sizeof(s_byResponse);
                    ptParamOut->pData = s_byResponse;
                }
            }
            else
            {
                PRINTERR("Invalid TRANSDATA: pData=%p, len=%u\n",
                             ptParamIn->pData, ptParamIn->dwDataLen);
                eRet = NVR_ERR__PARAM_INVALID;
            }
        }
        break;

        case EDGE_COI_EVENT_LISTENING:
            PRINTDBG("Received EVENT_LISTENING message\n");
            // 可以在这里处理其他事件监听
            break;

        case EDGE_COI_DEVICE_SNAPSHOOT:
            PRINTDBG("Received DEVICE_SNAPSHOOT message\n");
            // 可以在这里处理抓图命令
            break;

        default:
            PRINTDBG("Unsupported COI message type: %d\n", eMsgType);
            break;
    }

    return eRet;
}

/**
 * @brief 处理服务端透传数据
 * @param pData 数据指针
 * @param dwDataLen 数据长度
 * @return 执行结果
 */
static NVRSTATUS NvrFixCoiProcessTransData(u8* pData, u32 dwDataLen)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    if(!pData || dwDataLen < 1)
    {
        PRINTERR("Invalid transdata parameters: pData=%p, len=%u\n", pData, dwDataLen);
        return NVR_ERR__PARAM_INVALID;
    }

    // 检测是否为JSON格式数据
    if(NvrFixCoiIsJsonData(pData, dwDataLen))
    {
        PRINTVIP("Processing JSON format transdata, len=%u\n", dwDataLen);

        // 确保数据以NULL结尾（JSON字符串需要）
        s8* pszJsonData = (s8*)malloc(dwDataLen + 1);
        if(!pszJsonData)
        {
            PRINTERR("Failed to allocate memory for JSON data\n");
            return NVR_ERR__ERROR;
        }

        memcpy(pszJsonData, pData, dwDataLen);
        pszJsonData[dwDataLen] = '\0';

        eRet = NvrFixCoiProcessJsonData(pszJsonData, dwDataLen);

        free(pszJsonData);
    }
    else
    {
        PRINTERR("Unsupported data format - only JSON format supported\n");
        eRet = NVR_ERR__PARAM_INVALID;
    }

    return eRet;
}

/**
 * @brief 检测是否为JSON格式数据
 * @param pData 数据指针
 * @param dwDataLen 数据长度
 * @return TRUE：JSON格式，FALSE：二进制格式
 */
static BOOL32 NvrFixCoiIsJsonData(const u8* pData, u32 dwDataLen)
{
    if(!pData || dwDataLen < 2)
        return FALSE;

    // 简单检测：JSON数据通常以'{'开始
    if(pData[0] == '{')
        return TRUE;

    // 检测是否包含JSON关键字段
    if(dwDataLen > 10)
    {
        s8 achCheckBuffer[32];
        u32 dwCheckLen = (dwDataLen > 31) ? 31 : dwDataLen;
        memcpy(achCheckBuffer, pData, dwCheckLen);
        achCheckBuffer[dwCheckLen] = '\0';

        if(strstr(achCheckBuffer, "alarmType") || strstr(achCheckBuffer, "alarmStatus"))
            return TRUE;
    }

    return FALSE;
}

/**
 * @brief 处理JSON格式的TRANSDATA消息
 * @param pszJsonData JSON字符串
 * @param dwDataLen 数据长度
 * @return 执行结果
 */
static NVRSTATUS NvrFixCoiProcessJsonData(const s8* pszJsonData, u32 dwDataLen)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    cJSON* pJsonObj = NULL;

    if(!pszJsonData || dwDataLen == 0)
    {
        PRINTERR("Invalid JSON data parameters\n");
        return NVR_ERR__PARAM_INVALID;
    }

    PRINTVIP("Processing JSON transdata: %s\n", pszJsonData);

    // 解析JSON
    pJsonObj = cJSON_Parse(pszJsonData);
    if(!pJsonObj)
    {
        PRINTERR("Failed to parse JSON data: %s\n", pszJsonData);
        return NVR_ERR__PARAM_INVALID;
    }

    // 检测JSON消息类型并处理
    cJSON* pAlarmType = cJSON_GetObjectItem(pJsonObj, "alarmType");
    cJSON* pAlarmStatus = cJSON_GetObjectItem(pJsonObj, "alarmStatus");

    if(pAlarmType && pAlarmStatus)
    {
        // 这是报警信令
        PRINTVIP("Detected alarm JSON message\n");
        eRet = NvrFixCoiProcessJsonAlarm(pJsonObj);
    }
    else
    {
        PRINTERR("Unknown JSON message format - missing required fields\n");
        eRet = NVR_ERR__PARAM_INVALID;
    }

    // 清理JSON对象
    cJSON_Delete(pJsonObj);

    return eRet;
}

/**
 * @brief 处理JSON格式的报警信息
 * @param pJsonObj JSON对象
 * @return 执行结果
 */
static NVRSTATUS NvrFixCoiProcessJsonAlarm(cJSON* pJsonObj)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrFixCoiJsonAlarmInfo tJsonAlarmInfo;
    TNvrAlarmSrc tAlarmSrc;
    TNvrBrokenDownTime tAlarmTime;
    cJSON* pItem = NULL;

    if(!pJsonObj)
    {
        PRINTERR("pJsonObj is NULL\n");
        return NVR_ERR__PARAM_INVALID;
    }

    // 初始化结构体
    memset(&tJsonAlarmInfo, 0, sizeof(tJsonAlarmInfo));

    // 解析alarmType
    pItem = cJSON_GetObjectItem(pJsonObj, "alarmType");
    if(pItem)
    {
        tJsonAlarmInfo.dwAlarmType = (u32)pItem->valueint;
    }
    else
    {
        PRINTERR("Missing or invalid alarmType field\n");
        return NVR_ERR__PARAM_INVALID;
    }

    // 解析alarmStatus
    pItem = cJSON_GetObjectItem(pJsonObj, "alarmStatus");
    if(pItem)
    {
        tJsonAlarmInfo.dwAlarmStatus = (u32)pItem->valueint;
    }
    else
    {
        PRINTERR("Missing or invalid alarmStatus field\n");
        return NVR_ERR__PARAM_INVALID;
    }

    // 解析extraData (可选)
    pItem = cJSON_GetObjectItem(pJsonObj, "extraData");
    if(pItem)
    {
        strncpy(tJsonAlarmInfo.achExtraData, pItem->valuestring, sizeof(tJsonAlarmInfo.achExtraData) - 1);
    }

    PRINTVIP("JSON Alarm parsed: type=%u, status=%u, chn=%u, no=%u, extraData='%s'\n",
                  tJsonAlarmInfo.dwAlarmType, tJsonAlarmInfo.dwAlarmStatus,
                  tJsonAlarmInfo.wChnId, tJsonAlarmInfo.wAlarmNo, tJsonAlarmInfo.achExtraData);

    // 根据alarmStatus值判断是否触发报警
    if(tJsonAlarmInfo.dwAlarmStatus == 0)
    {
        // alarmStatus = 0，取消报警
        PRINTVIP("Alarm status is 0, canceling alarm (type=%u)\n", tJsonAlarmInfo.dwAlarmType);
    }
    else if(tJsonAlarmInfo.dwAlarmStatus == 1)
    {
        // alarmStatus = 1，触发报警
        PRINTVIP("Alarm status is 1, triggering alarm (type=%u)\n", tJsonAlarmInfo.dwAlarmType);
    }
    else
    {
        // 无效的alarmStatus值
        PRINTERR("Invalid alarmStatus value: %u\n", tJsonAlarmInfo.dwAlarmStatus);
        return NVR_ERR__PARAM_INVALID;
    }

    // 准备报警源信息
    memset(&tAlarmSrc, 0, sizeof(tAlarmSrc));
    tAlarmSrc.wDevId = tJsonAlarmInfo.wChnId;
    tAlarmSrc.byAlarmType = (u8)(tJsonAlarmInfo.dwAlarmType & 0xFF); // 取低8位
    tAlarmSrc.byAlarmNum = (u8)(tJsonAlarmInfo.wAlarmNo & 0xFF);

    // 获取当前时间
    memset(&tAlarmTime, 0, sizeof(tAlarmTime));
    NvrSysGetSystemLocalTime(&tAlarmTime);

    PRINTVIP("Calling alarm interface: devid=%u, type=%u, num=%u, status=%u, desc='%s'\n",
                  tAlarmSrc.wDevId, tAlarmSrc.byAlarmType, tAlarmSrc.byAlarmNum,
                  tJsonAlarmInfo.dwAlarmStatus, tJsonAlarmInfo.achExtraData);

    // 调用nvralarm模块触发或取消报警
    // alarmStatus: 0=取消报警, 1=触发报警
    eRet = NvrAlarmStatusChangeNotify(tAlarmSrc, (u8)tJsonAlarmInfo.dwAlarmStatus,
                                     tAlarmTime, tJsonAlarmInfo.achExtraData,
                                     NVR_APP_PROTO_ONVIF);

    if(NVR_ERR__OK == eRet)
    {
        if(tJsonAlarmInfo.dwAlarmStatus == 1)
        {
            PRINTVIP("JSON alarm triggered successfully (type=%u)\n", tJsonAlarmInfo.dwAlarmType);
        }
        else
        {
            PRINTVIP("JSON alarm canceled successfully (type=%u)\n", tJsonAlarmInfo.dwAlarmType);
        }
    }
    else
    {
        PRINTERR("Failed to process JSON alarm: status=%u, error=%d\n",
                      tJsonAlarmInfo.dwAlarmStatus, eRet);
    }

    return eRet;
}

/**
 * @brief 打印十六进制数据 (调试用)
 * @param pszPrefix 前缀字符串
 * @param pData 数据指针
 * @param dwDataLen 数据长度
 */
static void NvrFixCoiPrintHexData(const s8* pszPrefix, u8* pData, u32 dwDataLen)
{
    if(!pszPrefix || !pData || dwDataLen == 0)
        return;

    // 限制打印长度，避免日志过长
    u32 dwPrintLen = (dwDataLen > 64) ? 64 : dwDataLen;

    PRINTDBG("%s [%u bytes]: ", pszPrefix, dwDataLen);

    for(u32 i = 0; i < dwPrintLen; i++)
    {
        printf("%02x ", pData[i]);
        if((i + 1) % 16 == 0)
            printf("\n                ");
    }

    if(dwDataLen > dwPrintLen)
        printf("... (truncated)");

    printf("\n");
}

/**
 * @brief 向服务端发送TRANSDATA消息
 * @param pData 数据指针
 * @param dwDataLen 数据长度
 * @param dwType 数据类型
 * @return 执行结果
 */
NVRSTATUS NvrFixCoiSendTransDataToServer(u8* pData, u32 dwDataLen, u32 dwType)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TEdgeCoiParam tInputParam = {0};

    if(!g_bNvrFixCoiInited)
    {
        PRINTERR("NvrFixCoi not initialized\n");
        return NVR_ERR__PARAM_INVALID;
    }

    if(!pData || dwDataLen == 0)
    {
        PRINTERR("Invalid parameters for sending transdata\n");
        return NVR_ERR__PARAM_INVALID;
    }

    // 准备输入参数
    tInputParam.dwType = dwType;
    tInputParam.dwDataLen = dwDataLen;
    tInputParam.pData = pData;

    PRINTVIP("Sending TRANSDATA to server, len=%u, type=%u\n", dwDataLen, dwType);

    // 打印发送的数据（调试用）
    NvrFixCoiPrintHexData("TX", pData, dwDataLen);

    // 通过nvrcoi发送到coiapp，再由coiapp发送到服务端
    eRet = EdgeCoiServiceMsgDataNotify(EDGE_COI_TRANSDATA, &tInputParam, NULL, NULL);
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("Send transdata to server failed: %d\n", eRet);
        return eRet;
    }

    PRINTDBG("Send transdata to server success\n");
    return NVR_ERR__OK;
}

/**
 * @brief 获取统计信息
 * @param ptStats 统计信息指针
 * @return 执行结果
 */
NVRSTATUS NvrFixCoiGetStatistics(TNvrFixCoiStatistics* ptStats)
{
    if(!ptStats)
    {
        PRINTERR("ptStats is NULL\n");
        return NVR_ERR__PARAM_INVALID;
    }

    ptStats->dwTransDataMsgCount = g_dwTransDataMsgCount;
    ptStats->dwTransDataSuccCount = g_dwTransDataSuccCount;
    ptStats->dwTransDataFailCount = g_dwTransDataFailCount;
    ptStats->bInited = g_bNvrFixCoiInited;

    return NVR_ERR__OK;
}

/**
 * @brief 初始化NvrFixCoi模块
 * @return 执行结果
 */
NVRSTATUS NvrFixCoiInit(void)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    if(g_bNvrFixCoiInited)
    {
        PRINTERR("NvrFixCoi already initialized\n");
        return NVR_ERR__OK;
    }

    PRINTVIP("Initializing NvrFixCoi module...\n");

    // 初始化统计计数器
    g_dwTransDataMsgCount = 0;
    g_dwTransDataSuccCount = 0;
    g_dwTransDataFailCount = 0;

    // 注册COI服务层消息处理回调
    eRet = EdgeCoiServiceMsgDataGetRegCB(NvrFixCoiServiceMsgHandler, NULL);
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("EdgeCoiServiceMsgDataGetRegCB failed: %d\n", eRet);
        return eRet;
    }

    g_bNvrFixCoiInited = TRUE;

    PRINTVIP("NvrFixCoi module initialized successfully\n");

    return NVR_ERR__OK;
}

/**
 * @brief 反初始化NvrFixCoi模块
 * @return 执行结果
 */
NVRSTATUS NvrFixCoiUninit(void)
{
    if(!g_bNvrFixCoiInited)
    {
        PRINTERR("NvrFixCoi not initialized\n");
        return NVR_ERR__OK;
    }

    PRINTVIP("Uninitializing NvrFixCoi module...\n");

    // TODO: 注销回调函数 (nvrcoi模块目前没有提供注销接口)

    g_bNvrFixCoiInited = FALSE;

    PRINTVIP("NvrFixCoi module uninitialized\n");

    return NVR_ERR__OK;
}
