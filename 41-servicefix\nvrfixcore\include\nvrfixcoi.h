/**
 * @file nvrfixcoi.h
 * @brief 业务层COI相关处理的头文件定义
 * <AUTHOR> Code
 * @date 2025-01-01
 * @version 1.0
 * @copyright V1.0 Copyright(C) 2025 NVR All rights reserved.
 */

#ifndef _NVRFIXCOI_H_
#define _NVRFIXCOI_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "nvrdef.h"
#include "coidef.h"
#include "nvrcoi.h"

// NvrFixCoi模块统计信息
typedef struct tagNvrFixCoiStatistics
{
    BOOL32 bInited;                 // 模块是否已初始化
    u32 dwTransDataMsgCount;        // 接收到的TRANSDATA消息总数
    u32 dwTransDataSuccCount;       // 处理成功的TRANSDATA消息数
    u32 dwTransDataFailCount;       // 处理失败的TRANSDATA消息数
} TNvrFixCoiStatistics;

/**
 * @brief 初始化NvrFixCoi模块
 * @return 执行结果
 * @retval NVR_ERR__OK 成功
 * @retval 其他 失败错误码
 * @note 该函数会注册COI服务层消息处理回调，用于接收服务端TRANSDATA消息
 */
NVRSTATUS NvrFixCoiInit(void);

/**
 * @brief 反初始化NvrFixCoi模块
 * @return 执行结果
 * @retval NVR_ERR__OK 成功
 * @retval 其他 失败错误码
 */
NVRSTATUS NvrFixCoiUninit(void);

/**
 * @brief COI服务层消息处理回调函数
 * @param eMsgType 消息类型
 * @param ptParamIn 输入参数
 * @param ptParamOut 输出参数 (可选)
 * @param pContext 上下文 (可选)
 * @return 执行结果
 * @retval NVR_ERR__OK 成功
 * @retval 其他 失败错误码
 * @note 该函数主要处理EDGE_COI_TRANSDATA类型的消息
 */
NVRSTATUS NvrFixCoiServiceMsgHandler(EEdgeCOIMsgType eMsgType, TEdgeCoiParam *ptParamIn, TEdgeCoiParam *ptParamOut, void* pContext);

/**
 * @brief 向服务端发送TRANSDATA消息
 * @param pData 数据指针
 * @param dwDataLen 数据长度
 * @param dwType 数据类型
 * @return 执行结果
 * @retval NVR_ERR__OK 成功
 * @retval NVR_ERR__INVALID_PARAM 参数无效
 * @retval NVR_ERR__SYSTEM_NOT_INIT 系统未初始化
 * @retval 其他 失败错误码
 * @note 通过nvrcoi -> coiapp -> 服务端的路径发送透传数据
 */
NVRSTATUS NvrFixCoiSendTransDataToServer(u8* pData, u32 dwDataLen, u32 dwType);

/**
 * @brief 获取NvrFixCoi模块统计信息
 * @param ptStats 统计信息指针
 * @return 执行结果
 * @retval NVR_ERR__OK 成功
 * @retval NVR_ERR__INVALID_PARAM 参数无效
 */
NVRSTATUS NvrFixCoiGetStatistics(TNvrFixCoiStatistics* ptStats);

#ifdef __cplusplus
}
#endif

#endif // _NVRFIXCOI_H_