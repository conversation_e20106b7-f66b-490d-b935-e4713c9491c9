#include "coidef.h"
#include "nvrcoi.h"
#include <stdio.h>

static PFEdgeCoiAppMsgDataNotifyCB g_pfAppMsgDataNotifyCB; 
static PFEdgeCoiServiceMsgDataGetCB g_pfServiceMsgDataGetCB;
static EEdgeCoiMsgDoorType g_ENvrCoiDoorType = EDGE_COI_MSG_DOOR_TYPE_NONE;
static u32 g_dwMrDbOpTimeStamp = 0xFFFFFFFF;

NVRSTATUS EdgeCoiInit()
{
	return NVR_ERR__OK;
}


///<===========================================to coiapp========================================================================

NVRSTATUS EdgeCoiAppMsgDataNotifyRegCB(PFEdgeCoiAppMsgDataNotifyCB pfCoiAppMsgCB, void* pContext)
{
	///<多次注册会重复覆盖之前的回调函数
	g_pfAppMsgDataNotifyCB = pfCoiAppMsgCB;

	printf("reg g_pfAppMsgDataNotifyCB suc !!\n");
	return NVR_ERR__OK;
}

NVRSTATUS EdgeCoiAppMsgDataSend(EEdgeCOIMsgType eMsgType, TEdgeCoiParam *ptParamIn, TEdgeCoiParam *ptParamOut, void* pContext)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	if(NULL != g_pfServiceMsgDataGetCB)
	{
		eRet = g_pfServiceMsgDataGetCB(eMsgType, ptParamIn, ptParamOut, pContext);
		
	}
	else
	{
		printf("err, g_pfServiceMsgDataGetCB is null !!\n");
		eRet = NVR_ERR__ERROR;
	}

	printf("EdgeCoiAppMsgDataSend ret = %d", eRet);

	return eRet;
}

EEdgeCoiMsgDoorType EdgeCoiAppGetDoortType()
{
	return g_ENvrCoiDoorType;
}


NVRSTATUS EdgeCoiAppGetDBDataStamp(u32* pdwTime)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	 *pdwTime =g_dwMrDbOpTimeStamp;
	return eRet;
}



///<===========================================to service========================================================================


NVRSTATUS EdgeCoiServiceMsgDataGetRegCB(PFEdgeCoiServiceMsgDataGetCB pfCoiServiceMsgCB, void* pContext)
{
	///<多次注册会重复覆盖之前的回调函数
	g_pfServiceMsgDataGetCB = pfCoiServiceMsgCB;

	printf("reg g_pfServiceMsgDataGetCB suc !!\n");
	return NVR_ERR__OK;
}




NVRSTATUS EdgeCoiServiceMsgDataNotify(EEdgeCOIMsgType eMsgType, TEdgeCoiParam *ptParamIn, TEdgeCoiParam *ptParamOut, void* pContext)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	if(NULL != g_pfAppMsgDataNotifyCB)
	{
		g_pfAppMsgDataNotifyCB(eMsgType, ptParamIn, ptParamOut, pContext);
		
	}
	else
	{
		printf("err, g_pfAppMsgDataNotifyCB is null !!\n");
		eRet = NVR_ERR__ERROR;
	}

	printf("EdgeCoiServiceMsgDataNotify ret = %d", eRet);

	return eRet;
}

NVRSTATUS EdgeCoiAppSetDoortType(EEdgeCoiMsgDoorType eDoorType)
{
	g_ENvrCoiDoorType = eDoorType;
	return NVR_ERR__OK;
}

NVRSTATUS EdgeCoiAppSetDBDataStamp(u32 dwTime)
{
	g_dwMrDbOpTimeStamp = dwTime;
	return NVR_ERR__OK;
}

