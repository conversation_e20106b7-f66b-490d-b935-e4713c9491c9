###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ./

SRC_DIR := $(TOP)/source


## Name and type of the target for this Makefile

SO_TARGET      := coiapp

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER= _ARM_HIS3531DV200_
PWLIB_SUPPORT = 0

CFLAGS += -D_LINUX -Wall -D_HIS3531DV200_ -D__arm__ -fno-omit-frame-pointer

OBJS := $(SRC_DIR)/coiapp \
        $(SRC_DIR)/coiapp_context \
		$(SRC_DIR)/coiapp_tool \
		$(SRC_DIR)/coiapp_crossplat_nvrv7 \
		$(SRC_DIR)/coiapp_shareaction \
		$(SRC_DIR)/coiapp_ws_handler \
		$(SRC_DIR)/coiapp_maintask  \
		$(SRC_DIR)/coiproto_endec \
		$(SRC_DIR)/coiapp_addtask \
		../ProtoFile/coiapp.pb-c

## Libraries to include in shared object file
        
#LIBS :=  

## Add driver-specific include directory to the search path
##ARC_LIBS += 
INC_PATH += ../include 										\
			../../../10-common/include/cbb/libwebsockets 	\
			../../../10-common/include/cbb/libwebsockets/config_file/linux \
			../../../10-common/include/cbb/openssl			\
			../../../10-common/include/cbb/goahead/linux 	\
            ../../../10-common/include/cbb/osp 				\
		    ../../../10-common/include/cbb/debuglog       	\
			../../../10-common/include/cbb/protobuf       	\
			../../../10-common/include/cbb/cjson       	\
            ../../../10-common/include/system             	\
            ../../../10-common/include/service            	\
            ../../../10-common/include/app \
			../../../10-common/include/app/lwshelper \
			../../../10-common/include/cbb/curl \
			../ProtoFile

LIB_PATH := ../../../10-common/lib/release/his3531dv200 \

LIBS := lwshelper \
		nvrcoi	\
		appbase    \
		debuglog \
		websockets \
		curl	\
		cjson

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3531dv200/applib

include $(COMM_DIR)/makelib.mk

