/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: coiapp.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "coiapp.pb-c.h"
void   coi_app__cfg__init
                     (CoiApp__Cfg         *message)
{
  static CoiApp__Cfg init_value = COI_APP__CFG__INIT;
  *message = init_value;
}
size_t coi_app__cfg__get_packed_size
                     (const CoiApp__Cfg *message)
{
  assert(message->base.descriptor == &coi_app__cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t coi_app__cfg__pack
                     (const CoiApp__Cfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &coi_app__cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t coi_app__cfg__pack_to_buffer
                     (const CoiApp__Cfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &coi_app__cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
CoiApp__Cfg *
       coi_app__cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (CoiApp__Cfg *)
     protobuf_c_message_unpack (&coi_app__cfg__descriptor,
                                allocator, len, data);
}
void   coi_app__cfg__free_unpacked
                     (CoiApp__Cfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &coi_app__cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor coi_app__cfg__field_descriptors[7] =
{
  {
    "Enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(CoiApp__Cfg, has_enable),
    offsetof(CoiApp__Cfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ServerIP",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(CoiApp__Cfg, serverip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ServerPort",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(CoiApp__Cfg, has_serverport),
    offsetof(CoiApp__Cfg, serverport),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "UrlPath",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(CoiApp__Cfg, urlpath),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "devid",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(CoiApp__Cfg, devid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "UserName",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(CoiApp__Cfg, username),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Password",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(CoiApp__Cfg, password),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned coi_app__cfg__field_indices_by_name[] = {
  0,   /* field[0] = Enable */
  6,   /* field[6] = Password */
  1,   /* field[1] = ServerIP */
  2,   /* field[2] = ServerPort */
  3,   /* field[3] = UrlPath */
  5,   /* field[5] = UserName */
  4,   /* field[4] = devid */
};
static const ProtobufCIntRange coi_app__cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor coi_app__cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "CoiApp.Cfg",
  "Cfg",
  "CoiApp__Cfg",
  "CoiApp",
  sizeof(CoiApp__Cfg),
  7,
  coi_app__cfg__field_descriptors,
  coi_app__cfg__field_indices_by_name,
  1,  coi_app__cfg__number_ranges,
  (ProtobufCMessageInit) coi_app__cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
