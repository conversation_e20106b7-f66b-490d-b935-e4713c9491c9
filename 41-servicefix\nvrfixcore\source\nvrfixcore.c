/**
* @file 	nvrfixcore
* @brief    nvr fix枪机相关业务，包括探针上传等
* <AUTHOR>
* @date 	2019-05-9
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/

#include "nvrbrdapi.h"
#include "mediactrl.h"			///<拼接坐标转换接口需要用到媒控头文件
#include "nvrpui.h"
#include "nvrftp.h"
#include "nvrprobe.h"
#include "nvrguard.h"
#include "nvrcap_in.h"
#include "ftpc_api.h"
#include "nvrfixcore.h"
#include "nvrfixdev.h"
#include "nvrosd.h"
#include "isp_interface.h"
#include "nvrproduct.h"
#include "nvrusrmgr_in.h"
#include "nvrsys.h"
#include "nvrjnimsgdef.h"
#include "charconv.h"
#include "nvrmpu.h"
#include "nvrrec.h"
#include "nvrrecservice.h"
#include <sys/time.h>
#include "nvrfixcfg.h"
#include "nvrfixisp.h"
#include "nvrfixmc.h"
#include "nvrfixdev.h"
#include "lcamclt.h"
#include "nvrfixplugin.h"
#include "nvrsystem.h"
#include "nvrfixprofeintelli.h"
#include "nvrcoi.h"
#include "airpsrv.h"
#include "drvlib_api.h"
#include "pigeonapp_share.h"
#include "pdnsapp_share.h"
#ifdef _SSC339G_
#include "ais_in.h"
#include "pubsecstack_tool.h"
#endif

#define IT_EPTZALG_SET_BLK_POS "IT_EPTZALG_SET_BLK_POS"  ///<7201 教师跟踪ptz控制移动的共享函数信息

#define NVR_FIX_MAX_MULTISERIAL_NUM 4
#define NVR_FIX_E2PBACKUP_PATH	"usr/config/e2pinfo.dat"	///<e2prom备份文件路径

static TNvrCapFixInernalCapInfo g_tNvrFixInterCap;                  ///< 全局设备私有能力 （ 如后续有动态修改能力，需要及时更新全局能力）

///抓拍触发事件类型
ENvrRecSnpEvt g_eFixMulitSrcSnpEvt[NVR_MAX_CHN_NUM+2];				///<抓拍触发的事件类型

extern SEMHANDLE g_hNvrFtpUploadSem;//ftp上传信号量
extern s8 g_achPtzVer[NVR_FIX_DEV_MAX_SOFTVER_LEN + 1];//云台版本号
static SEMHANDLE g_hWifQDFileSem;//远端ftp探针数据文件操作异步信号量
static SEMHANDLE g_hHSCSem;//主从通讯信号量
static BOOL g_bIsHost = FALSE;//是否是多目主机设备
static PFNvrAlarmStatusChangeCB  g_pfNvrOnvirproAPICBkNotify = NULL;///<从片上报主片函数指针
static TIspLedInfo g_tLastLedInfo[NVR_MAX_LCAM_CHN_NUM];//红外
static SEMHANDLE g_hFixSnapSem = NULL;						///<抓拍操作同步信号量
static SERIALHANDLE g_hMultiSerialHandle[NVR_FIX_MAX_MULTISERIAL_NUM] = {-1, -1, -1, -1};	///<串口句柄
static SEMHANDLE g_hMultiSerialSem = NULL;   ///<电警多串口测试信号量
static TASKHANDLE   g_hShowIspTask[NVR_MAX_LCAM_CHN_NUM] = {(TASKHANDLE)NULL,(TASKHANDLE)NULL,(TASKHANDLE)NULL,(TASKHANDLE)NULL};
static BOOL32 s_bShowIspInfo[NVR_MAX_LCAM_CHN_NUM] = {FALSE};

static u8 g_bySerialTestFlag = 0;  ///<串口测试标志
static TASKHANDLE   g_hSerialEchoTask = (TASKHANDLE)NULL;

static TASKHANDLE   g_h5012UpdateTask = (TASKHANDLE)NULL;

static HTIMERHANDLE g_hSlaveUpgradeTimer = NULL;           //延时处理定时器


static BOOL32 g_bSlaveUpdating = FALSE;   ///<从机是否在升级
static BOOL32 g_bSlaveNeedUpdate = FALSE;   ///<从机是否需要升级




static void NvrProbeWriteFtpFile(s8* pbyDataBuf, u32 dwDataLen);
static NVRSTATUS NvrFtpGeneralTimePath(TNvrFtpServerParam *ptFtpServerParam,s8* achServerPath, u32 dwLen);
static NVRSTATUS NvrFtpProbePut(s8* achServerPath);
static void NvrFtpProbeUpload(TNvrGuardCBParam *pCBParam);
void NvrFixLampCtrl(u8 byLedId, u8 byLedStr);
static s32 NvrIspIrCb(void *pParam);
static void NvrFixInitInfrared();
static void NvrIspSetInfrared(u8 byChnId,u8 *pbyLedStr);
static void NvrFixInitLdr();
static s32 NvrFixIspLDRCb(void * pParam);
static NVRSTATUS NvrFixIspGetLDR(u32 *pdwValue);
static void NvrFixHostReceiveSlaveMessageCB(u16 wChnId,ENvrAlarmType eAlarmType,u16 wAlarmNo, u8 byAlarmStatus, void *pData , void *pContext);
static NVRSTATUS NvrFixHostToSlaveMessage(ENvrOnvifTransDataType eReportDataType, void *pbyBuf);
static void NvrFixSlaveReceiveHostMessageCB(char * pbyMsgBuf, u16 wBufLen, char * pbyMsgReturnBuf, u16 * pwReturnBufLen);
static void NvrFixSetDnThreshold(u8 byValue);
static void NvrFixSwitchIrcut(u8 status);
static void NvrFixMultiSerialTest();
static void NvrFixSerialEchoTest(u8 byTestFlag);

static void NvrFixGetXs5012UpdateFlag();
static void NvrFixXs5012Update();
static void NvrFixSetXs5012Update();

static void NvrFixXs5012Version();


static void NvrFixClearE2promInfo();
static void NvrFixSetTestFlag(u8 byFlag);
static void NvrFixShowIspInfo(u8 byEnable, u8 byChn);

static HTIMERHANDLE g_hTimer = NULL;                            //延时处理定时器
TNvrOnvifDevMsg g_tOnvifDevMsg;
ENvrRecSnpEvt g_eFixSnpEvt;				                        ///<抓拍触发的事件类型

s32 g_nInfraredVal[IPC_CORE_MAX_NFRARED_NUM] = {0};             ///<红外灯亮度值记录（用于加热策略） index 0:近光  1：远光
extern TNvrLifeStat g_aLifeStat[NVR_MAX_LCAM_CHN_NUM];          ///<寿命统计相关信息

/**PTZ设置板书位置*/
typedef struct tagEptzSetBlkPos
{
	u32 u32ver;//版本号
	u32 u32type;//类型
	u32 u32speed;//速度
	u32 u32reserved[10];//预留
}TNvrEptzSetBlkPos; 

u32 NvrFixCoreParseCmdlineSysMemInfo()
{
    FILE *fp;
    u32  dwSysMemSize = 0;
    s8 *pTmpStr = NULL, *pEndStr = NULL;
    static s8 sLine[1024];

    if(NULL == (fp = fopen("/proc/cmdline", "r")))
    {
        perror("fopen");
        return 0;
    }

    mzero(sLine);

    while (NULL != fgets(sLine, 1024, fp))
    {
        pTmpStr = strstr(sLine, "sz=");
        if (NULL != pTmpStr)
        {
            pTmpStr += 3;
            dwSysMemSize = strtol(pTmpStr, &pEndStr, 16);
        }
    }
    fclose(fp);

    return dwSysMemSize;
}


void NvrProbeWriteFtpFile(s8* pbyDataBuf, u32 dwDataLen)
{           
    FILE *fp;
    s8 achTime[NVR_MAX_STR32_LEN+1] = {0};
    s8 achFile[NVR_MAX_STR256_LEN+1] = {0};
    s8 achOut[NVR_MAX_STR256_LEN+1];
    TNvrWifiManger tNvrWifiManger;
	TNvrBrokenDownTime tLocalTime;

    mzero(tNvrWifiManger);
    mzero(tLocalTime);
    
    NvrWifiGetParam(&tNvrWifiManger,0);

    mzero(achOut);

    if(tNvrWifiManger.tApCfg.bUploadWProbeFtp != TRUE)
    {
        return;
    }

    ///Get current time
    NvrSysGetSystemLocalTime(&tLocalTime);

    //信息内容
    snprintf(achTime,NVR_MAX_STR32_LEN,"%04d-%02d-%02d %02d:%02d:%02d",tLocalTime.wYear,tLocalTime.byMonth,tLocalTime.byDay,tLocalTime.byHour,tLocalTime.byMinute,tLocalTime.bySecond);
    snprintf(achOut,sizeof(achOut),"%s*%s\n",achTime,pbyDataBuf);

    //文件路径
    snprintf(achFile,sizeof(achFile),"%s%s", NVR_PROBE_SAVE_PATH,NVR_PROBE_TMP_FTP_NAME);

    OsApi_SemTake(g_hWifQDFileSem);

    if(NULL != (fp = fopen(achFile, "a+")))
    {        
        fwrite(achOut, 1, strlen(achOut), fp);
        PRINTERR("%s write to file:%s\n",achOut, achFile);
        
        fclose(fp);

    }
    else
    {
        PRINTERR("open file %s failed.\n", achFile);
    }

    OsApi_SemGive(g_hWifQDFileSem);

    return;
}


NVRSTATUS NvrFtpGeneralTimePath(TNvrFtpServerParam *ptFtpServerParam,s8* achServerPath, u32 dwLen)
{
	FIX_ASSERT(achServerPath);

	s32 nFtpRet = FTPC_OK;
	NVRSTATUS eRet = NVR_ERR__OK;
	s8 achSerPath[NVR_MAX_STR256_LEN];
	TNvrBrokenDownTime tLocalTime;

	mzero(achSerPath);
	mzero(tLocalTime);
	
	NvrSysGetSystemLocalTime(&tLocalTime);

	if (NVR_FTP_DIR_STRUCT_ROOT == ptFtpServerParam->eDirStruct)
	{
	}
	else if (NVR_FTP_DIR_STRUCT_LEVEL1 == ptFtpServerParam->eDirStruct || NVR_FTP_DIR_STRUCT_LEVEL2 == ptFtpServerParam->eDirStruct)
	{
		switch(ptFtpServerParam->eL1Name)
		{
			case NVR_FTP_L1_NAME_DEVNAME:
			{
				TNvrSysParam tNvrSysParam;
				char achDevName[(NVR_SYS_MAX_DEVNANE_LEN+1)*2];

				mzero(tNvrSysParam);
				mzero(achDevName);
				
				NvrSysGetSysParam(&tNvrSysParam);
				CharConvConvertUnicodetoUtf8(tNvrSysParam.abyDevName, tNvrSysParam.wDevNameLen, achDevName, sizeof(achDevName));
				snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%s", achDevName);
			}
			break;

			case NVR_FTP_L1_NAME_DEVIP:
			{
				u8 *pbyIndex = NULL;
				TNvrNetEthParam tEthParam;

				mzero(tEthParam);

				NvrNetworkGetEthParam(0, &tEthParam);

				pbyIndex = (u8*)&tEthParam.tEthCfgParam.tIpv4EthParam.atIpAddr[0].dwIpv4Addr;

				snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%u.%u.%u.%u", pbyIndex[0], pbyIndex[1], pbyIndex[2], pbyIndex[3]);
			}
			break;

			case NVR_FTP_L1_NAME_TIME:
			{					
				snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%4d%02d%02d", tLocalTime.wYear, tLocalTime.byMonth, tLocalTime.byDay);
			}
			break;

			case NVR_FTP_L1_NAME_CUSTOM:
			{
				s8 achDirL1Custom[(NVR_MAX_STR32_LEN+1)*2];  	

				mzero(achDirL1Custom);

				CharConvConvertUnicodetoUtf8((u8*)ptFtpServerParam->achDirL1Custom, ptFtpServerParam->dwDirL1CustomLen, achDirL1Custom, sizeof(achDirL1Custom));
				
				snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%s", achDirL1Custom);

			}
			break;

			default:
			{
				PRINTERR("server L1 name invalid:%d\n", ptFtpServerParam->eL1Name);
				return NVR_ERR__ERROR;
			}
			break;
			
		}

		///<先创建一级目录
		nFtpRet = FTPCMkdir(achSerPath);
		if (FTPC_OK != nFtpRet && FTPC_MKDERR != nFtpRet)		///<目录已经创建会返回FTPC_MKDERR
		{
			PRINTERR("ftpc mkdir L1 failed ret=%d\n", nFtpRet);
			return NVR_ERR__ERROR;
		}
		PRINTDBG("ftpc mkdir L1:%s ret=%d\n", achSerPath, nFtpRet);

		
		if (NVR_FTP_DIR_STRUCT_LEVEL2 == ptFtpServerParam->eDirStruct)
		{
			switch(ptFtpServerParam->eL2Name)
			{
				case NVR_FTP_L2_NAME_CHNID:
				{
					snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%d", 0);
				}
				break;

				case NVR_FTP_L2_NAME_DEVID:
				{
					TNvrSysDevInfo tNvrSysDevInfo;
					
					mzero(tNvrSysDevInfo);
					
					NvrSysGetDevInfo(&tNvrSysDevInfo);

					snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%s", tNvrSysDevInfo.achDevSerialNum);
				}
				break;

				case NVR_FTP_L2_NAME_CUSTOM:
				{	
					s8 achDirL2Custom[(NVR_MAX_STR32_LEN+1)*2];  	

					mzero(achDirL2Custom);

					CharConvConvertUnicodetoUtf8((u8*)ptFtpServerParam->achDirL2Custom, ptFtpServerParam->dwDirL2CustomLen, achDirL2Custom, sizeof(achDirL2Custom));
					
					snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%s", achDirL2Custom);
				}
				break;

				default:
				{
					PRINTERR("server L2 name invalid:%d\n", ptFtpServerParam->eL1Name);
					return NVR_ERR__ERROR;
				}
				break;
			}

			nFtpRet = FTPCMkdir(achSerPath);
			if (FTPC_OK != nFtpRet && FTPC_MKDERR != nFtpRet)		///<目录已经创建会返回FTPC_MKDERR
			{
				PRINTERR("ftpc mkdir failed ret=%d\n", nFtpRet);
				return NVR_ERR__ERROR;
			}
			PRINTDBG("ftpc mkdir:%s ret=%d\n", achSerPath, nFtpRet);
		}
	}
	else
	{
		PRINTERR("server dir struct invalid:%d\n", ptFtpServerParam->eDirStruct);
		return NVR_ERR__ERROR;
	}
	
	snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%4d%02d%02d", tLocalTime.wYear, tLocalTime.byMonth, tLocalTime.byDay);

	///<先创建一级目录
	nFtpRet = FTPCMkdir(achSerPath);
	if (FTPC_OK != nFtpRet && FTPC_MKDERR != nFtpRet)		///<目录已经创建会返回FTPC_MKDERR
	{
		PRINTERR("ftpc mkdir L1 failed ret=%d\n", nFtpRet);
		return NVR_ERR__ERROR;
	}
	PRINTDBG("ftpc mkdir L1:%s ret=%d\n", achSerPath, nFtpRet);

    snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%02d", tLocalTime.byHour);

	nFtpRet = FTPCMkdir(achSerPath);
	if (FTPC_OK != nFtpRet && FTPC_MKDERR != nFtpRet)		///<目录已经创建会返回FTPC_MKDERR
	{
		PRINTERR("ftpc mkdir L2 failed ret=%d\n", nFtpRet);
		return NVR_ERR__ERROR;
	}
	PRINTDBG("ftpc mkdir:L2 %s ret=%d\n", achSerPath, nFtpRet);

    snprintf(achSerPath+strlen(achSerPath), sizeof(achSerPath)-strlen(achSerPath), "/%02d", tLocalTime.byMinute);

	nFtpRet = FTPCMkdir(achSerPath);
	if (FTPC_OK != nFtpRet && FTPC_MKDERR != nFtpRet)		///<目录已经创建会返回FTPC_MKDERR
	{
		PRINTERR("ftpc mkdir L3 failed ret=%d\n", nFtpRet);
		return NVR_ERR__ERROR;
	}
	PRINTDBG("ftpc mkdir:L3 %s ret=%d\n", achSerPath, nFtpRet);

	memcpy(achServerPath, achSerPath, dwLen);

	return eRet;
	
}


NVRSTATUS NvrFtpProbePut(s8* achServerPath)
{
	FIX_ASSERT(achServerPath);

	s32 nFtpRet = FTPC_OK;
	NVRSTATUS eRet = NVR_ERR__OK;
	s8 achLocalPicPath[NVR_MAX_STR256_LEN];
	s8 achLocalPicName[NVR_MAX_STR256_LEN];
	s8 achServerPicName[NVR_MAX_STR256_LEN];
	s8 achLocalPicPathName[NVR_MAX_STR256_LEN];
	TNvrBrokenDownTime tLocalTime;

	mzero(tLocalTime);
	mzero(achServerPicName);
	mzero(achLocalPicPath);
	mzero(achLocalPicName);
	mzero(achLocalPicPathName);

	NvrSysGetSystemLocalTime(&tLocalTime);

#if defined(_WIN64) || defined(WIN64) ||defined (__LP64__) || defined (__64BIT__) || defined (_LP64) || (__WORDSIZE == 64)
    snprintf((s8 *)achServerPicName, sizeof(achServerPicName), "%u%02u%02u%02u%02u%02u%03u_%s_%s_%s_%s.log",    tLocalTime.wYear, tLocalTime.byMonth, tLocalTime.byDay,
                         tLocalTime.byHour, tLocalTime.byMinute, tLocalTime.bySecond, tLocalTime.dwMsec, NVR_FTPSERV_DATA_SRC_TYPE, NVR_FTPSERV_DATA_SRC_IDE,
                         NVR_FTPSERV_DATA_FACTORY, NVR_FTPSERV_DATA_TYPE);
#else
    snprintf((s8 *)achServerPicName, sizeof(achServerPicName), "%u%02u%02u%02u%02u%02u%03lu_%s_%s_%s_%s.log",    tLocalTime.wYear, tLocalTime.byMonth, tLocalTime.byDay,
                         tLocalTime.byHour, tLocalTime.byMinute, tLocalTime.bySecond, tLocalTime.dwMsec, NVR_FTPSERV_DATA_SRC_TYPE, NVR_FTPSERV_DATA_SRC_IDE, 
                         NVR_FTPSERV_DATA_FACTORY, NVR_FTPSERV_DATA_TYPE);
#endif
    sprintf(achLocalPicName,NVR_PROBE_TMP_FTP_NAME);
    sprintf(achLocalPicPath,NVR_PROBE_SAVE_PATH);
    snprintf(achLocalPicPathName,sizeof(achLocalPicPathName),"%s%s", NVR_PROBE_SAVE_PATH,NVR_PROBE_TMP_FTP_NAME);
        
	PRINTDBG("ftpc put pic server:%s %s local:%s %s\n", achServerPath, achServerPicName, achLocalPicPath, achLocalPicName);

    OsApi_SemTake(g_hWifQDFileSem);

    nFtpRet = FTPCPutFile(achServerPath, achServerPicName, achLocalPicName, achLocalPicPath);
	if (FTPC_OK != nFtpRet)
	{
		PRINTERR("ftpc put file failed ret=%d\n", nFtpRet);
        OsApi_SemGive(g_hWifQDFileSem);
		return NVR_ERR__ERROR;
	}

    if(access(achLocalPicPathName, F_OK) == 0)
    {
        remove(achLocalPicPathName);
    }

    OsApi_SemGive(g_hWifQDFileSem);

	return eRet;
}

void NvrFtpProbeUpload(TNvrGuardCBParam *pCBParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	s32 nFtpRet = FTPC_OK;
	s8 achSerPath[NVR_MAX_STR256_LEN];
	TNvrFtpServerParam tFtpServerParam;
	TNvrWifiManger tNvrWifiManger;
	char achIP[64] = {};
	
	mzero(achSerPath);
	mzero(tFtpServerParam);
	mzero(tNvrWifiManger);

    NvrWifiGetParam(&tNvrWifiManger,0);

    if(tNvrWifiManger.tApCfg.bUploadWProbeFtp != TRUE)
    {
        return;
    }

    OsApi_SemTake(g_hNvrFtpUploadSem);

	do 
	{    
		NvrFtpGetFtpServerParam(&tFtpServerParam);
	
	    if (NULL == NvrFtpNetAddr2Strr(&tFtpServerParam.tServerAddr, achIP, sizeof (achIP)))
	    {
	        PRINTERR("NvrNetAddr2Strr error\n");
            eRet = NVR_ERR__ERROR;
            break;
	    }

		///<设置服务器ip端口
		PRINTDBG("ftpc setserv addr ip=%s port=%lu\n", achIP, tFtpServerParam.dwServerPort);
		nFtpRet = FTPCSetServAddr(achIP, tFtpServerParam.dwServerPort);
		if (FTPC_OK != nFtpRet)
		{
			PRINTERR("ftpc setserv addr failed ret=%d\n", nFtpRet);
			eRet = NVR_ERR__ERROR;
			break;
		}

	
		///<登录FTP服务器
		PRINTDBG("ftpc setlogid anony=%lu username:%s passwd:%s\n", tFtpServerParam.bAnonymous, tFtpServerParam.achUserName, tFtpServerParam.achPasswd);

		if (tFtpServerParam.bAnonymous)	///<匿名登录
		{
			nFtpRet = FTPCSetLogId("anonymous", "");
		}
		else
		{
			nFtpRet = FTPCSetLogId(tFtpServerParam.achUserName, tFtpServerParam.achPasswd);
		}
		if (FTPC_OK != nFtpRet)
		{
			PRINTERR("ftpc setlogid failed ret=%d\n", nFtpRet);
			eRet = NVR_ERR__ERROR;
			break;
		}

		///<创建服务器存储路径
		eRet = NvrFtpGeneralTimePath(&tFtpServerParam,achSerPath, sizeof(achSerPath));
		if (NVR_ERR__OK != eRet)
		{
			PRINTERR("general server path failed ret=%d\n", eRet);           
            eRet = NVR_ERR__ERROR;
			break;
		}

		eRet = NvrFtpProbePut(achSerPath);
		if (NVR_ERR__OK != eRet)
        {      
			PRINTERR("pic put to server failed ret=%d\n", eRet);
            eRet = NVR_ERR__ERROR;
			break;
		}

	}
	while(0);

	///<关闭FTP链接 
	nFtpRet = FTPCCloseAll();
	if (FTPC_OK != nFtpRet)
	{
		PRINTERR("ftpc close all failed ret=%d\n", nFtpRet);
        eRet = NVR_ERR__ERROR;
	}

	if (NVR_ERR__OK == eRet)
	{
		PRINTDBG("upload success\n");
	}
	else
	{
		PRINTDBG("upload failed!!!\n");
	}

    OsApi_SemGive(g_hNvrFtpUploadSem);

	return;

}



NVRSTATUS NvrFixLcamMessageCb(ELcamSpecialCtrlType eType, void* pParam)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    u16 wChnId = 0;
    TNvrPtzCtrlInfo tPtzCtrlInfo;
    u32 i = 0;
    TLcamIspParam tIspParam;
    TLcamMcPtzParam tPTZParam;
	TNvrCapSortwareCapInfo tCapSoftware;
	TNvrDevSmartTransParam tCtrlSourceParam;
    TNvrCapHwCapInfo tHwCapInfo;
	
    mzero(tPtzCtrlInfo);
    mzero(tIspParam);
    mzero(tPTZParam);
	mzero(tCapSoftware);
	mzero(tCtrlSourceParam);
    mzero(tHwCapInfo);
	
	PRINTTMP("NvrFixLcamMessageCb eType %u\n",eType);
    NvrCapGetCapParam(NVR_CAP_ID_HW, &tHwCapInfo);
	
    switch(eType)
    {
    case LCAM_SPE_CTRL_TYPE_PTZ_CTRL:
        {
            TNvrLcamSpecialPtzCtrl *ptCtrlInfo = (TNvrLcamSpecialPtzCtrl*)pParam;


			tCtrlSourceParam.eSourceType = ptCtrlInfo->ptCtrlInfo->bySourceType;
			NvrPuiSetCurrentSourceType(&tCtrlSourceParam);

            wChnId = ptCtrlInfo->dwDevID-1;
			tPtzCtrlInfo.eCtrlType = NvrFixDevTypeConvert(ptCtrlInfo->ptCtrlInfo->eCtrlType);
            tPtzCtrlInfo.wIspSpeed = ptCtrlInfo->ptCtrlInfo->wIspSpeed;
            tPtzCtrlInfo.wPanSpeed = ptCtrlInfo->ptCtrlInfo->wPanSpeed;
            tPtzCtrlInfo.wTilSpeed = ptCtrlInfo->ptCtrlInfo->wTilSpeed;
			tPtzCtrlInfo.bySourceType = ptCtrlInfo->ptCtrlInfo->bySourceType;
			tPtzCtrlInfo.dwRes = ptCtrlInfo->ptCtrlInfo->dwRes;
			tPtzCtrlInfo.eMode = ptCtrlInfo->ptCtrlInfo->eMode;
			///<预置位加载、删除lcamclt过来的number从1开始，需要做下转换从0开始,花样扫描同理
			if(APP_CLT_PTZ_CTRL_PRESET_REMOVE == ptCtrlInfo->ptCtrlInfo->eCtrlType || APP_CLT_PTZ_CTRL_PRESET_LOAD == ptCtrlInfo->ptCtrlInfo->eCtrlType || APP_CLT_PTZ_CTRL_PRESET_SET == ptCtrlInfo->ptCtrlInfo->eCtrlType
				||APP_CLT_PTZ_SYNCSCAN_REC == ptCtrlInfo->ptCtrlInfo->eCtrlType||APP_CLT_PTZ_SYNCSCAN_STOPREC == ptCtrlInfo->ptCtrlInfo->eCtrlType||
				APP_CLT_PTZ_SYNCSCAN_PREVIEW == ptCtrlInfo->ptCtrlInfo->eCtrlType||APP_CLT_PTZ_SYNCSCAN_DELETE == ptCtrlInfo->ptCtrlInfo->eCtrlType)
			{
				tPtzCtrlInfo.wNumber = ptCtrlInfo->ptCtrlInfo->wNumber - 1;
			}
			else
			{
				tPtzCtrlInfo.wNumber = ptCtrlInfo->ptCtrlInfo->wNumber;
			}
			tPtzCtrlInfo.eMode = ptCtrlInfo->ptCtrlInfo->eMode;
			tPtzCtrlInfo.wWide = ptCtrlInfo->ptCtrlInfo->wWidth;
			tPtzCtrlInfo.wHeight = ptCtrlInfo->ptCtrlInfo->wHeight;
			if(APP_CLT_PTZ_CTRL_ZOOMPART == ptCtrlInfo->ptCtrlInfo->eCtrlType || APP_CLT_PTZ_CTRL_ZOOMWHOLE == ptCtrlInfo->ptCtrlInfo->eCtrlType || APP_CLT_PTZ_CTRL_GOTOPOINT == ptCtrlInfo->ptCtrlInfo->eCtrlType)
			{
				///<画布宽高为0，实际宽高为255*255
				if(0 == ptCtrlInfo->ptCtrlInfo->wWinMaxWidth || 0 == ptCtrlInfo->ptCtrlInfo->wWinMaxHeight)
				{
					tPtzCtrlInfo.wWinWide = 255;
					tPtzCtrlInfo.wWinHeight = 255;	
				}
				else
				{
					tPtzCtrlInfo.wWinWide = ptCtrlInfo->ptCtrlInfo->wWinMaxWidth;
					tPtzCtrlInfo.wWinHeight = ptCtrlInfo->ptCtrlInfo->wWinMaxHeight;
				}		
				///<将APP_CLT_PTZ_POINT_CENTER下对应的x/y坐标转换成APP_CLT_PTZ_POINT_LEFT_UP下对应的值
				///<APP_CLT_PTZ_POINT_CENTER对应的画布原点在画面中心，传过来的x/y坐标对应的是框的左上角的坐标
				///<APP_CLT_PTZ_POINT_LEFT_UP对应的画布原点在画面左上角，传过来的x/y坐标对应的cu对应的是框的中心点的坐标，web为左上角顶点
				if(APP_CLT_PTZ_POINT_CENTER == ptCtrlInfo->ptCtrlInfo->eCSYSType)
				{	
					///<转换原点坐标
					if(ptCtrlInfo->ptCtrlInfo->wXPosition <= tPtzCtrlInfo.wWinWide/2 && ptCtrlInfo->ptCtrlInfo->wYPosition <= tPtzCtrlInfo.wWinHeight/2)
					{
						tPtzCtrlInfo.wXposition = tPtzCtrlInfo.wWinWide/2 + ptCtrlInfo->ptCtrlInfo->wXPosition;
						tPtzCtrlInfo.wYposition = tPtzCtrlInfo.wWinHeight/2 - ptCtrlInfo->ptCtrlInfo->wYPosition;
					}
					else if(ptCtrlInfo->ptCtrlInfo->wXPosition <= tPtzCtrlInfo.wWinWide/2 && ptCtrlInfo->ptCtrlInfo->wYPosition > tPtzCtrlInfo.wWinHeight/2)
					{
						tPtzCtrlInfo.wXposition = tPtzCtrlInfo.wWinWide/2 + ptCtrlInfo->ptCtrlInfo->wXPosition;
						tPtzCtrlInfo.wYposition = tPtzCtrlInfo.wWinHeight/2 + (tPtzCtrlInfo.wWinHeight - ptCtrlInfo->ptCtrlInfo->wYPosition);
					}
					else if(ptCtrlInfo->ptCtrlInfo->wXPosition > tPtzCtrlInfo.wWinWide/2 && ptCtrlInfo->ptCtrlInfo->wYPosition <= tPtzCtrlInfo.wWinHeight/2)
					{
						tPtzCtrlInfo.wXposition = ptCtrlInfo->ptCtrlInfo->wXPosition - tPtzCtrlInfo.wWinWide/2;
						tPtzCtrlInfo.wYposition = tPtzCtrlInfo.wWinHeight/2 - ptCtrlInfo->ptCtrlInfo->wYPosition;
					}
					else if(ptCtrlInfo->ptCtrlInfo->wXPosition > tPtzCtrlInfo.wWinWide/2 && ptCtrlInfo->ptCtrlInfo->wYPosition > tPtzCtrlInfo.wWinHeight/2)
					{
						tPtzCtrlInfo.wXposition = ptCtrlInfo->ptCtrlInfo->wXPosition - tPtzCtrlInfo.wWinWide/2;
						tPtzCtrlInfo.wYposition = tPtzCtrlInfo.wWinHeight/2 + (tPtzCtrlInfo.wWinHeight - ptCtrlInfo->ptCtrlInfo->wYPosition);
					}

					///<转换x/y坐标为框中心点坐标
					tPtzCtrlInfo.wXposition = tPtzCtrlInfo.wXposition + tPtzCtrlInfo.wWide/2;
					tPtzCtrlInfo.wYposition = tPtzCtrlInfo.wYposition + tPtzCtrlInfo.wHeight/2;
					
				}
				else				
				{
					///<若画布宽高为10000*10000表示为web控制，x，y坐标为左上角,需转换为中心点，画布尺寸为其他表示平台，坐标为中心点
					if(10000 == tPtzCtrlInfo.wWinWide || 10000 == tPtzCtrlInfo.wWinHeight)
					{

						tPtzCtrlInfo.wXposition =  ptCtrlInfo->ptCtrlInfo->wXPosition+ tPtzCtrlInfo.wWide/2;
						tPtzCtrlInfo.wYposition = ptCtrlInfo->ptCtrlInfo->wYPosition + tPtzCtrlInfo.wHeight/2;

					}
					else
					{
						tPtzCtrlInfo.wXposition = ptCtrlInfo->ptCtrlInfo->wXPosition;
						tPtzCtrlInfo.wYposition = ptCtrlInfo->ptCtrlInfo->wYPosition;
					}
				
				} 
				///<实测下来，web传过来的x和y坐标在画面边缘的时候会超过画布宽高，做下处理
				if(tPtzCtrlInfo.wXposition > tPtzCtrlInfo.wWinWide)
					tPtzCtrlInfo.wXposition = tPtzCtrlInfo.wWinWide;
				if(tPtzCtrlInfo.wYposition > tPtzCtrlInfo.wWinHeight)
					tPtzCtrlInfo.wYposition = 1;
			}	
			else
			{
				tPtzCtrlInfo.wWinWide = ptCtrlInfo->ptCtrlInfo->wWinMaxWidth;
				tPtzCtrlInfo.wWinHeight = ptCtrlInfo->ptCtrlInfo->wWinMaxHeight;
				tPtzCtrlInfo.wXposition = ptCtrlInfo->ptCtrlInfo->wXPosition;
				tPtzCtrlInfo.wYposition = ptCtrlInfo->ptCtrlInfo->wYPosition;
			}
            
            eRet = NvrFixDevPtzCtrl(wChnId,tPtzCtrlInfo);
#ifdef _CV2X_
			TNvrCapLcam tNvrCapLcam;
			
			mzero(tNvrCapLcam);
			NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);

			//当移动时调用函数
			if(NVR_CAP_LCAMMC_PARAM_SPECIAL_IPC7201 == tNvrCapLcam.tLcamMcInfo.eParamSpecial 
				&& ptCtrlInfo->ptCtrlInfo->eCtrlType < APP_CLT_PTZ_CTRL_ZOOM_TELE
				&& ptCtrlInfo->ptCtrlInfo->eCtrlType > APP_CLT_PTZ_CTRL_UNKNOWN
				&& 1 == wChnId)
			{
				TNvrEptzSetBlkPos tSetBlkPos = { 0 };

				tSetBlkPos.u32ver = 1;
				tSetBlkPos.u32type = ptCtrlInfo->ptCtrlInfo->eCtrlType;
				tSetBlkPos.u32speed = (tPtzCtrlInfo.wPanSpeed == 0)?tPtzCtrlInfo.wTilSpeed:tPtzCtrlInfo.wPanSpeed;
			
				EAppResult eShareRet = APP_OK;
				EAppResult eAppRet = AppRunShareAction(IT_EPTZALG_SET_BLK_POS, (void*)&tSetBlkPos, NULL, NULL, &eShareRet);
				printf("eAppRet:%d,eShareRet:%d,APP_OK,%d\n",eAppRet,eShareRet,APP_OK);
			}
#endif    
		}
        break;
    case LCAM_SPE_CTRL_TYPE_PTZ_UPGRADE_START:
        {
            TNvrLcamSpecialPtzUpgradeStart *ptStartPkg = (TNvrLcamSpecialPtzUpgradeStart*)pParam;

             eRet = NvrFixDevUpgradeStart(ptStartPkg->dwDevID, ptStartPkg->dwPkgTotalSize,ptStartPkg->pbyPkgHeadBuf,ptStartPkg->dwPkgHeadBufSize,ptStartPkg->pfStateReport,ptStartPkg->dwOpeque);
        }
        break;
    case LCAM_SPE_CTRL_TYPE_PTZ_UPGRADE_SEND_PKG:
        {
            TNvrLcamSpecialPtzUpgradeSendPkg *ptSendPkg = (TNvrLcamSpecialPtzUpgradeSendPkg*)pParam;

            eRet = NvrFixDevUpgradeSendPkgData(ptSendPkg->dwDevID,ptSendPkg->dwDataBeginPos,ptSendPkg->pbyDataBuf,ptSendPkg->dwDataBufSize);
        }
        break;
    case LCAM_SPE_CTRL_TYPE_DEV_CFG_GET:
        {
            TNvrLcamSpecialDevCfg *ptDevCfg = (TNvrLcamSpecialDevCfg*)pParam;
			
            if(APP_CLT_DEV_CFG_PTZ_BASE_INF == ptDevCfg->eType )
            {
            	TNvrPtzBasicState tIpcPtzState;
				TAppCltDevCfgPtzBaseInf* ptAppCltDevCfgPtzBaseInf = (TAppCltDevCfgPtzBaseInf*)ptDevCfg->pvDevCfgArr;
				
				mzero(tIpcPtzState);
				NvrFixCfgGetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
                
				memcpy(&ptAppCltDevCfgPtzBaseInf->tCfg,&tIpcPtzState,sizeof(TNvrPtzBasicState));
                strncpy(ptAppCltDevCfgPtzBaseInf->tCfg.achPtzSoftVer, g_achPtzVer, sizeof(ptAppCltDevCfgPtzBaseInf->tCfg.achPtzSoftVer)); ///<云台版本号
                //ptAppCltDevCfgPtzBaseInf->tCfg.ePTOsdMode = NVR_DEV_PT_MODE_NUMBER;
            }
			else if (APP_CLT_DEV_CFG_PTZ_POWER_OFF_RSM == ptDevCfg->eType )
			{
				TNvrPowerOffRsmCfg tPowerOffRsmCfg;
				mzero(tPowerOffRsmCfg);
				NvrFixCfgGetParamByFlag(&tPowerOffRsmCfg,sizeof(TNvrPowerOffRsmCfg),NVR_PTZ_MODULE_POWE_RSM_PARAM);
				
				TAppCltDevCfgPtzPowerOffRsm* ptAppCltDevCfgPtzPowerOffRsm = (TAppCltDevCfgPtzPowerOffRsm*)ptDevCfg->pvDevCfgArr;
				memcpy(&ptAppCltDevCfgPtzPowerOffRsm->tCfg,&tPowerOffRsmCfg,sizeof(TNvrPowerOffRsmCfg));
				ptAppCltDevCfgPtzPowerOffRsm->tCfg.adwParam[NVR_DEV_POWEROFF_LOAD_PRESET] = tPowerOffRsmCfg.adwParam[NVR_DEV_POWEROFF_LOAD_PRESET];
			}
			else if(APP_CLT_DEV_CFG_PTZ_WATCHON == ptDevCfg->eType)
			{
				TNvrWatchOnParam tWatchOn;
				mzero(tWatchOn);
				
				NvrFixCfgGetParamByFlag(&tWatchOn,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);
				TAppCltDevCfgPtzWatchOn* ptAppCltDevCfgPtzWatchOn = (TAppCltDevCfgPtzWatchOn*)ptDevCfg->pvDevCfgArr;
				ptAppCltDevCfgPtzWatchOn->bEnable = tWatchOn.bEnable;
				ptAppCltDevCfgPtzWatchOn->dwPathCruiseID = tWatchOn.dwPathCruiseID;
				ptAppCltDevCfgPtzWatchOn->dwPresetID = tWatchOn.dwPresetID;
				ptAppCltDevCfgPtzWatchOn->dwSyncScanID = tWatchOn.dwSyncScanID;
				ptAppCltDevCfgPtzWatchOn->dwWaitTimeSec = tWatchOn.dwWaitTimeSec;
				ptAppCltDevCfgPtzWatchOn->eTaskType = NvrFixDevWatchOnTypeConvert2AppClt(tWatchOn.byTaskType);
				NvrFixCfgGetParamByFlag(&tWatchOn,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);


			}
			else if(APP_CLT_DEV_CFG_PTZ_PRESET == ptDevCfg->eType)
			{
				TNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM]={0};
				NvrFixCfgGetParamByFlag(atPresetInfo,NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo),NVR_PTZ_MODULE_PRESET_PARAM);
				TAppCltDevCfgPtzPreset* ptAppCltDevCfgPreset = (TAppCltDevCfgPtzPreset*)ptDevCfg->pvDevCfgArr;
				u16  wNum = ptAppCltDevCfgPreset->tCfg.wPresetNum;
				u8 achbuf[NVR_PUI_PRESET_NAME_BUF_LEN] = {0};
				
				u32 dwLen = sizeof(achbuf);

				///<特殊预置位，需要根据语言对预置位名称进行转换	
				if(g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySpecialPreset)
				{
					NvrFixCfgSpecPreAliasConvBylan(wNum, &atPresetInfo[wNum], ptAppCltDevCfgPreset->tCfg.eLanguageType);
				}
				CharConvConvertUtf8toUnicode((s8*)atPresetInfo[wNum].abyAlias, achbuf, &dwLen);
				atPresetInfo[wNum].dwAliasLen = dwLen;
				memcpy(atPresetInfo[wNum].abyAlias,achbuf,atPresetInfo[wNum].dwAliasLen);
				memcpy(&ptAppCltDevCfgPreset->tCfg.tPresetInfo,&atPresetInfo[ptAppCltDevCfgPreset->tCfg.wPresetNum],sizeof(TNvrPresetInfo));
			}
			else if(APP_CLT_DEV_CFG_PTZ_LIMIT == ptDevCfg->eType)
			{
				TNvrPtzBasicState tIpcPtzState;
				mzero(tIpcPtzState);
				NvrFixCfgGetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
				TAppCltDevCfgPtzLimit* ptAppCltDevCfgPtzLimit = (TAppCltDevCfgPtzLimit*)ptDevCfg->pvDevCfgArr;
				ptAppCltDevCfgPtzLimit->tCfg.dwManuLimitPos = tIpcPtzState.dwManuLimitPos;
				ptAppCltDevCfgPtzLimit->tCfg.dwScanLimitPos = tIpcPtzState.dwScanLimitPos;
				
                printf("get manlimit:"FORMAT_U32",scanlimit:"FORMAT_U32"\n",tIpcPtzState.dwManuLimitPos,tIpcPtzState.dwScanLimitPos);

			}
			else if(APP_CLT_DEV_CFG_PTZ_PATH_CRS == ptDevCfg->eType)
			{		
				TNvrPathCrsInfo tPathCrsInfo;
				TAppCltDevCfgPtzPathCrs* ptAppCltDevCfgPathCrs = (TAppCltDevCfgPtzPathCrs*)ptDevCfg->pvDevCfgArr;
				u8 achbuf[128] = {0};
				u32 dwLen = sizeof(achbuf);
				u16 wNum;
				TNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM]={0};
				
				NvrFixCfgGetParamByFlag(atPresetInfo,NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo),NVR_PTZ_MODULE_PRESET_PARAM);
				mzero(tPathCrsInfo);
				NvrFixCfgGetPathCrsInfo(ptAppCltDevCfgPathCrs->tCfg.byPathNum, &tPathCrsInfo);
				///<路径巡航配置中保存的预置位信息并不是最新状态，需要获取对应预置位配置中的信息
				for(i = 0; i < tPathCrsInfo.wPresetCnt; i++)
				{
					memset(achbuf, 0, sizeof(achbuf));
					dwLen = sizeof(achbuf);
					wNum = tPathCrsInfo.tPresetInfo[i].wPrenum;
					CharConvConvertUtf8toUnicode((s8*)atPresetInfo[wNum].abyAlias,achbuf,&dwLen);
					tPathCrsInfo.tPresetInfo[i].bIsSet = atPresetInfo[wNum].bIsSet;
					tPathCrsInfo.tPresetInfo[i].dwAliasLen =  dwLen;
					memcpy(tPathCrsInfo.tPresetInfo[i].abyAlias,achbuf,tPathCrsInfo.tPresetInfo[i].dwAliasLen);
				}			
				memcpy(&ptAppCltDevCfgPathCrs->tCfg.tPathCrsInfo, &tPathCrsInfo, sizeof(TNvrPathCrsInfo));
			}
			else if(APP_CLT_DEV_CFG_PTZ_SYNC_SCAN == ptDevCfg->eType)
			{
				TNvrSyncScanInfo tSyncScanInfo;
				mzero(tSyncScanInfo);
				NvrFixCfgGetParamByFlag(&tSyncScanInfo,sizeof(TNvrSyncScanInfo),NVR_PTZ_MODULE_SYNS_SCAN_INFO_PARAM);
				TAppCltDevCfgPtzSyncScan* ptAppCltDevCfgPtzSyncScan = (TAppCltDevCfgPtzSyncScan*)ptDevCfg->pvDevCfgArr;
				//NvrFixDevGetSyncScanInfo(&tSyncScanInfo);
				memcpy(&ptAppCltDevCfgPtzSyncScan->tCfg,&tSyncScanInfo,sizeof(TNvrSyncScanInfo));
			}
			else if(APP_CLT_DEV_CFG_PTZ_TIMING_TASK == ptDevCfg->eType)
			{
				TNvrTmingTaskParam tTimeTask;
				mzero(tTimeTask);
				
				NvrFixCfgGetParamByFlag(&tTimeTask,sizeof(TNvrTmingTaskParam),NVR_PTZ_MODULE_TIMING_TASK_PARAM);
				TAppCltDevCfgPtzTimingTask* ptAppCltDevCfgPtzTimingTask = (TAppCltDevCfgPtzTimingTask*)ptDevCfg->pvDevCfgArr;
				memcpy(&ptAppCltDevCfgPtzTimingTask->tCfg,&tTimeTask,sizeof(TNvrTmingTaskParam));
			}
			else if(APP_CLT_DEV_CFG_PTZ_COORDINATE == ptDevCfg->eType)
			{
				TAppCltDevCfgPtzCoo* ptAppCltDevCfgPtzCoo = (TAppCltDevCfgPtzCoo*)ptDevCfg->pvDevCfgArr;
				u32 dwHAngle = 0;
				u32 dwVAngle = 0;
				
				NvrFixDevGetCurrentPTAngel(&dwHAngle, &dwVAngle);
				//<返回映射之后的角度
				if(g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPtzPAngleTransformate == NVR_CAP_SUPPORT
				        || g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPtzTAngleTransformate == NVR_CAP_SUPPORT)
				{
					NvrFixDevPTAngelConvert2Vir(dwHAngle, dwVAngle, &dwHAngle, &dwVAngle);
				}
				
				ptAppCltDevCfgPtzCoo->tCfg.nX = dwHAngle;
				ptAppCltDevCfgPtzCoo->tCfg.nY = dwVAngle<=9000?dwVAngle:(s32)(dwVAngle%36000-36000);
			}
			else if(APP_CLT_DEV_CFG_LIFE_STAT == ptDevCfg->eType)
			{
				TAppCltDevCfgLifeStat *ptAppCltDevCfgLifeStatParam = (TAppCltDevCfgLifeStat *)ptDevCfg->pvDevCfgArr;
				wChnId = ptDevCfg->dwDevID - 1;
					
                ptAppCltDevCfgLifeStatParam->wVidSrcID = wChnId;
                ptAppCltDevCfgLifeStatParam->dwSumTime = g_aLifeStat[wChnId].dwSumTime;
                ptAppCltDevCfgLifeStatParam->dwHighTempTime = g_aLifeStat[wChnId].dwHighTempTime;
                ptAppCltDevCfgLifeStatParam->dwLowTempTime = g_aLifeStat[wChnId].dwLowTempTime;
                ptAppCltDevCfgLifeStatParam->dwIcrChangeTimes = g_aLifeStat[wChnId].dwIcrChangeTimes;
                ptAppCltDevCfgLifeStatParam->dwHorizontalTurnTimes = g_aLifeStat[wChnId].dwHorizontalTurnTimes;
                ptAppCltDevCfgLifeStatParam->dwVerticalTurnTimes = g_aLifeStat[wChnId].dwVerticalTurnTimes;
                ptAppCltDevCfgLifeStatParam->dwZoomChangeTimes = g_aLifeStat[wChnId].dwZoomChangeTimes;

                ptAppCltDevCfgLifeStatParam->bHasHighTempTime = g_aLifeStat[wChnId].bHasHighTempTime;
                ptAppCltDevCfgLifeStatParam->bHasLowTempTime =  g_aLifeStat[wChnId].bHasLowTempTime;;
                ptAppCltDevCfgLifeStatParam->bHasIcrChangeTimes =  g_aLifeStat[wChnId].bHasIcrChangeTimes;;
                ptAppCltDevCfgLifeStatParam->bHasHorizontalTurnTimes =  g_aLifeStat[wChnId].bHasHorizontalTurnTimes;;
                ptAppCltDevCfgLifeStatParam->bHasVerticalTurnTimes =  g_aLifeStat[wChnId].bHasVerticalTurnTimes;;
                ptAppCltDevCfgLifeStatParam->bHasZoomChangeTimes =  g_aLifeStat[wChnId].bHasZoomChangeTimes;;
			}
			else if(APP_CLT_DEV_CFG_PTZ_ALL_PRESETS == ptDevCfg->eType)
			{
				TNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM]={0};
				u8 achbuf[NVR_PUI_PRESET_NAME_BUF_LEN] = {0};
				u32 dwLen = sizeof(achbuf);
				TNvrPresetParam tPresetParam;
				TAppCltDevCfgPtzAllPresets *ptAppCltDevCfgPtzAllPreset = (TAppCltDevCfgPtzAllPresets*)ptDevCfg->pvDevCfgArr;
				ENvrLanguageType eLanType = ptAppCltDevCfgPtzAllPreset->atPresets[0].eLanguageType;	///<获取全部预置位信息时，和web及cgi商定用第一个预置位对应的语言作为全部预置位的语言类型
					
				NvrFixCfgGetParamByFlag(atPresetInfo,NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo),NVR_PTZ_MODULE_PRESET_PARAM);
				for(i = 0; i < g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue; i++)
				{	
					memset(achbuf, 0, sizeof(achbuf));
					dwLen = sizeof(achbuf);
					
					///<特殊预置位，需要根据语言对预置位名称进行转换
					if(g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySpecialPreset)
					{
						NvrFixCfgSpecPreAliasConvBylan(i, &atPresetInfo[i], eLanType);
					}
					CharConvConvertUtf8toUnicode((s8*)atPresetInfo[i].abyAlias, achbuf, &dwLen);
					atPresetInfo[i].dwAliasLen = dwLen;
					memcpy(atPresetInfo[i].abyAlias,achbuf,atPresetInfo[i].dwAliasLen);
					
					mzero(tPresetParam);
					tPresetParam.wPresetNum = i;
					memcpy(&tPresetParam.tPresetInfo,&atPresetInfo[i],sizeof(TNvrPresetInfo));
					memcpy(&ptAppCltDevCfgPtzAllPreset->atPresets[i],&tPresetParam,sizeof(TNvrPresetParam));	
				}
				ptAppCltDevCfgPtzAllPreset->dwPresetNum = 256;//g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue;
				
			}
			else if(APP_CLT_DEV_CFG_CURRENT_PRESET_ID == ptDevCfg->eType)
			{
				TAppCltDevCfgCurrentPresetIdInfo *ptPresetIdInfo = (TAppCltDevCfgCurrentPresetIdInfo*)ptDevCfg->pvDevCfgArr;
				NvrFixDevGetCurrentPresetId(&ptPresetIdInfo->byPresetId);
			}
			else if(APP_CLT_DEV_CFG_CURRENT_ZOOM_RATIO == ptDevCfg->eType)
			{
				TAppCltDevCfgCurrentZoomRatio *ptZoomRatioInfo = (TAppCltDevCfgCurrentZoomRatio*)ptDevCfg->pvDevCfgArr;
				NvrFixDevGetCurrentZoomRatio((s32*)&ptZoomRatioInfo->dwZoomRatio);
				ptZoomRatioInfo->dwMinZoomRatio = 9;//g_dwMinZoomRatio;
				ptZoomRatioInfo->dwMaxZoomRatio = 19;//g_dwMaxZoomRatio;
			}
			else if(APP_CLT_DEV_CFG_ISP == ptDevCfg->eType)
			{
				TNvrIspDeMistCfg tDeMistTask;
				mzero(tDeMistTask);
				
				NvrFixCfgGetParamByFlag(&tDeMistTask,sizeof(TNvrIspDeMistCfg),NVR_PTZ_MODULE_DEMIST_PARAM);
				TAppCltDevCfgIsp *ptIspInfo = (TAppCltDevCfgIsp*)ptDevCfg->pvDevCfgArr;
				
				memcpy(&ptIspInfo->tDemist,&tDeMistTask,sizeof(TNvrIspDeMistCfg));
				
				PRINTDBG("NvrPtzLcamMessageCb  dwDeMistTime:%d\n",tDeMistTask.dwDeMistTime);
			
			}
			else if(APP_CLT_DEV_CFG_PTZ_ZOOM_VIEW_ANGLE == ptDevCfg->eType)
			{
				s32 nCurZoom = 0;			
				TAppCltDevCfgZoomViewAngleParam *ptZoomViewAngleParam = (TAppCltDevCfgZoomViewAngleParam*)ptDevCfg->pvDevCfgArr;

				NvrFixDevGetZoomPosition(&nCurZoom);
				ptZoomViewAngleParam->nZmPos = nCurZoom;
				NvrFixGetZoomHAngle((u32)nCurZoom, &ptZoomViewAngleParam->fHAngle);
				NvrFixGetZoomVAngle((u32)nCurZoom, &ptZoomViewAngleParam->fVAngle);
				
				PRINTDBG("NvrPtzLcamMessageCb APP_CLT_DEV_CFG_PTZ_ZOOM_VIEW_ANGLE, nZmPos:%d, fHAngle: %f, fVAngle: %f\n", ptZoomViewAngleParam->nZmPos, ptZoomViewAngleParam->fHAngle, ptZoomViewAngleParam->fVAngle);
			}
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_ZOOM_PARAM)
            {
				s32 nCurZoom = 0;
                TAppCltDevCfgPtzZoomParam *ptAppCltDevCfgPtzZoomParam = (TAppCltDevCfgPtzZoomParam *)ptDevCfg->pvDevCfgArr;

                NvrFixDevGetZoomPosition(&nCurZoom);
				ptAppCltDevCfgPtzZoomParam->nZmPos = nCurZoom;
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_FOCUS_PARAM)
            {
				s32 nCurFocus = 0;
                TAppCltDevCfgPtzFocusParam *ptAppCltDevCfgPtzFocusParam = (TAppCltDevCfgPtzFocusParam *)ptDevCfg->pvDevCfgArr;

				NvrFixDevGetFocusPosition(&nCurFocus);
				ptAppCltDevCfgPtzFocusParam->nFcsPos = nCurFocus;
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_LIGHT_INTENSITY)
            {
                TAppCltDevCfgLightParam *ptAppCltDevCfgLightParam = (TAppCltDevCfgLightParam *)ptDevCfg->pvDevCfgArr;

                eRet = LcamIspGetParam(0, &tIspParam);
                if(NVR_ERR__OK != eRet)
                {
                    PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CFG_GET get isp param failed ret:%d\n", eRet);
                    return NVR_ERR__ERROR;
                }

                ///<目前最大支持2组灯亮度获取
                if(ptAppCltDevCfgLightParam->byLightId == 0)
                {
                    if(tIspParam.tInfrared.tInfaredLevel.bFarAndNearInfredCtrl)
                    {
                        ptAppCltDevCfgLightParam->dwLightIntensity = tIspParam.tInfrared.tInfaredLevel.tFarAndNearLevel.dwNear;
                    }
                    else
                    {
                       ptAppCltDevCfgLightParam->dwLightIntensity = tIspParam.tInfrared.tInfaredLevel.dwInfaredLevel;
                    }
                }
                else if(ptAppCltDevCfgLightParam->byLightId == 1)
                {
                    ptAppCltDevCfgLightParam->dwLightIntensity = tIspParam.tInfrared.tInfaredLevel.tFarAndNearLevel.dwFar;
                }
                else
                {
                    ptAppCltDevCfgLightParam->dwLightIntensity = 0;
                }
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_ACCELE_ANGLE)
            {
                TAppCltDevCfgAcceleAngleParam *ptAcceleAngleParam = (TAppCltDevCfgAcceleAngleParam *)ptDevCfg->pvDevCfgArr;
            
                eRet = LcamMcCalcAcceleAngle(&(ptAcceleAngleParam->dSlantAngle), &(ptAcceleAngleParam->dRotateAngle));
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_ZOOM_PARAM)
            {
                TAppCltDevCfgPtzZoomParam *ptAppCltDevCfgPtzZoomParam = (TAppCltDevCfgPtzZoomParam *)ptDevCfg->pvDevCfgArr;

                eRet = LcamMcGetPTZCfgChn(0, &tPTZParam);
                if(NVR_ERR__OK != eRet)
                {
                    PRINTERR("get PtzLockParam failed %d\n", eRet);
                    return NVR_ERR__ERROR;
                }

                ///<镜头锁定状态下采用断电记忆zoom值作为当前zoom位置
                if (TRUE == tPTZParam.bIsp)
                {
                    ptAppCltDevCfgPtzZoomParam->nZmPos = g_tPowerOffMemCfg[0].nZoomPos;
                }
                else
                {
                    IspActionParam(0, ISP_ACT_GET_ZOOM_POSITION, &(ptAppCltDevCfgPtzZoomParam->nZmPos));
                }
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_FOCUS_PARAM)
            {
                TAppCltDevCfgPtzFocusParam *ptAppCltDevCfgPtzFocusParam = (TAppCltDevCfgPtzFocusParam *)ptDevCfg->pvDevCfgArr;

                IspActionParam(0, ISP_ACT_GET_FOCUS_POSITION, &(ptAppCltDevCfgPtzFocusParam->nFcsPos));
            }
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_DUAL_OVERLAY)
            {
                TAppCltDevCfgDualChnOverlay *ptAppCltChnOverlayParam = (TAppCltDevCfgDualChnOverlay *)ptDevCfg->pvDevCfgArr;
				TNvrFixCfg tFixCfg;

				NvrFixCfgGetParam(&tFixCfg);

				ptAppCltChnOverlayParam->eDualChnOverlayPosSize = tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosSize;
				ptAppCltChnOverlayParam->eDualChnOverlayPosType = tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType;
            }
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_BASIC_INTEL_THERMEAS_TEMP)
            {
                TAppCltDevCfgTherMeas *ptAppCltTherMeasParam = (TAppCltDevCfgTherMeas *)ptDevCfg->pvDevCfgArr;
				TNvrFixCfg tFixCfg;
				u32 dwCurSel = 0;
				BOOL32 bHasCurSel = TRUE;

				dwCurSel = ptAppCltTherMeasParam->tParam.dwCurSel;
				bHasCurSel = ptAppCltTherMeasParam->tParam.bHasCurSel;

				NvrFixCfgGetParam(&tFixCfg);
				
				memcpy(&ptAppCltTherMeasParam->tParam,&tFixCfg.tIntelCfg.tTherMeasTemp,sizeof(TNvrIntelTherMeasTempParam));
				if(TRUE == bHasCurSel)
				{
					ptAppCltTherMeasParam->tParam.dwCurSel = dwCurSel;
				}		

				PRINTDBG("temp get cursel:%u %u-%u %u\n",tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel,tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempFormat,\
												dwCurSel,bHasCurSel);
            }
			else if (APP_CLT_DEV_CFG_PROFE_INTEL_SMOKE_FIRE == ptDevCfg->eType)
			{
			    TAppCltDevCfgSmokkeFire *ptAppCltSmokeFireParam = (TAppCltDevCfgSmokkeFire *)ptDevCfg->pvDevCfgArr;
			    TNvrIntelSmokeFireCfg   tSmokefire;             ///< 烟火识别参数
			    s32 nPresetId = ptAppCltSmokeFireParam->tParam.tPlanInfo.nPresetId;
			    wChnId = ptDevCfg->dwDevID - 1;

                NvrFixCfgGetParamById(wChnId, NVR_FIX_CFG_SMOKEFIRE, &tSmokefire, sizeof(tSmokefire));

                memcpy(&ptAppCltSmokeFireParam->tParam.tPlanInfo, &tSmokefire.atPreset[nPresetId], sizeof(TNvrIntelSmokeFirePlanInfo));

                ptAppCltSmokeFireParam->tParam.tPlanInfo.bEnable = tSmokefire.bEnable;
                ptAppCltSmokeFireParam->tParam.tPlanInfo.nPresetId = nPresetId;

                memcpy(&ptAppCltSmokeFireParam->tParam.tLinkAlarm, &tSmokefire.tLinkAlarm, sizeof(TNvrEventContact));
                memcpy(&ptAppCltSmokeFireParam->tParam.tOtherInfo, &tSmokefire.tOtherInfo, sizeof(TNvrIntelSmokeFireOtherInfo));

                PRINTDBG("smoke fire get %d presetid:%d(0:all), enable:%d c:%d s:%d \n", wChnId,
                        ptAppCltSmokeFireParam->tParam.tPlanInfo.nPresetId, ptAppCltSmokeFireParam->tParam.tPlanInfo.bEnable,
                        ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion, ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion);
                if (ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion || ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion)
                {
                    s32 ii = 0;
                    for (ii = 0; ii < ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion; ii++)
                    {
                        PRINTDBG("check indx:%d point num:%d\n", ii, ptAppCltSmokeFireParam->tParam.tPlanInfo.tCheckRegion[ii].dwPointNum);
                    }
                    for (ii = 0; ii < ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion; ii++)
                    {
                        PRINTDBG("shield indx:%d point num:%d\n", ii, ptAppCltSmokeFireParam->tParam.tPlanInfo.tShieldRegion[ii].dwPointNum);
                    }
                }
                PRINTDBG("smoke fire get mini obj %d %d %d %d \n", tSmokefire.tOtherInfo.wX, tSmokefire.tOtherInfo.wY, tSmokefire.tOtherInfo.wWidth, tSmokefire.tOtherInfo.wHight);
            }
            else if (APP_CLT_DEV_CFG_PROFE_INTEL_SMOKE_FIRE_LINK == ptDevCfg->eType )
            {
                TAppCltDevCfgAlarmLink *ptAppCltDevCfgPreventLinkParam = (TAppCltDevCfgAlarmLink *)ptDevCfg->pvDevCfgArr;
                TNvrAlarmProIntelLinkPattern tLinkPattern;
                mzero(tLinkPattern);

                MAKE_COMPILER_HAPPY(ptAppCltDevCfgPreventLinkParam);
            }
        }
        break;
    case LCAM_SPE_CTRL_TYPE_DEV_CAP_GET:
        {
            TNvrLcamSpecialDevCap *ptDevCap = (TNvrLcamSpecialDevCap*)pParam;

			
			if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ)
            {
                TAppCltDevCapPtz* ptCapPtz = (TAppCltDevCapPtz*)ptDevCap->pvCap;

                ///< onvif 根据ptCapPtz->bPtzSup 能力来控制p t z相关，支持zoom也需要上报
				ptCapPtz->bPtzSup =   g_tNvrFixInterCap.tFixPtzInternalCap.bPtzSup || g_tNvrFixInterCap.tFixHwInternalCap.bySupFocus;
                ptCapPtz->bBaseSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byBasicPosSupport;
    			ptCapPtz->bPwrOffResumeSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPowerOffResume;
				ptCapPtz->bWatchOnSup 		= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byWatchOnTask;
				ptCapPtz->bPresetSup 		= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPreset;
				ptCapPtz->bScanLimitSup 	= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPosLimit;
				ptCapPtz->bPathCruiseSup 	= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPathCruise;
				ptCapPtz->bTimingTaskSup 	= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byTimingTask;
				ptCapPtz->bCooSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySetPosSupport;
				ptCapPtz->bAutoWiperSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byWipeSupport;
				ptCapPtz->bAutoMaintain = TRUE;
				ptCapPtz->bSycnScanSup  = TRUE;

			}
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_BASE_INF)
            {
                TAppCltDevCapPtzBaseInf *ptCapPtzBase = (TAppCltDevCapPtzBaseInf*)ptDevCap->pvCap;

                memset(ptCapPtzBase,0,sizeof(TAppCltDevCapPtzBaseInf));
                ptCapPtzBase->tScanSpeed.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tScanSpeed.dwMinValue;
                ptCapPtzBase->tScanSpeed.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tScanSpeed.dwMaxValue;
				ptCapPtzBase->bZoomSpeedSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byZoomSpeedSupport;
				ptCapPtzBase->bVideoStopSup = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byVideoStopSupport;
				ptCapPtzBase->abPTOsdModeCap[NVR_DEV_PT_MODE_POINTER] = TRUE;
				ptCapPtzBase->abPTOsdModeCap[NVR_DEV_PT_MODE_NUMBER] = TRUE;
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_POWER_OFF_RSM)
            {
                TAppCltDevCapPtzPowerOffRsm *ptCapPtzPowerOffRsm = (TAppCltDevCapPtzPowerOffRsm*)ptDevCap->pvCap;

                memset(ptCapPtzPowerOffRsm,0,sizeof(TAppCltDevCapPtzPowerOffRsm));
                ptCapPtzPowerOffRsm->tPwrOffResumeInfo.abyResumeType[NVR_CAP_PTZ_POWEROFF_MEM] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.abyResumeType[NVR_CAP_PTZ_POWEROFF_MEM];
                ptCapPtzPowerOffRsm->tPwrOffResumeInfo.abyResumeType[NVR_CAP_PTZ_POWEROFF_LOAD_PRESET] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.abyResumeType[NVR_CAP_PTZ_POWEROFF_MEM];
			   	ptCapPtzPowerOffRsm->tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_MEM].dwMinValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_MEM].dwMinValue;
			   	ptCapPtzPowerOffRsm->tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_MEM].dwMaxValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_MEM].dwMaxValue;
				ptCapPtzPowerOffRsm->tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_LOAD_PRESET].dwMinValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_LOAD_PRESET].dwMinValue-1;
			   	ptCapPtzPowerOffRsm->tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_LOAD_PRESET].dwMaxValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPwrOffResumeInfo.atPwrOffResumeParam[NVR_CAP_PTZ_POWEROFF_LOAD_PRESET].dwMaxValue-1;
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_WATCHON)
            {
                TAppCltDevCapPtzWatchOn *ptCapPtzWatchOn = (TAppCltDevCapPtzWatchOn*)ptDevCap->pvCap;
                memset(ptCapPtzWatchOn,0,sizeof(TAppCltDevCapPtzWatchOn));
				
				ptCapPtzWatchOn->tWatchOn.abTaskSup[APP_CLT_HORIZON_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tWatchOnInfo.abyWatchOnTaskList[NVR_PTZCTRL_HORIZON_SCAN_TASK];
				ptCapPtzWatchOn->tWatchOn.abTaskSup[APP_CLT_PRESET_LOAD_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tWatchOnInfo.abyWatchOnTaskList[NVR_PTZCTRL_PRESET_LOAD_TASK];
				ptCapPtzWatchOn->tWatchOn.abTaskSup[APP_CLT_PATH_CRUISE_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tWatchOnInfo.abyWatchOnTaskList[NVR_PTZCTRL_PATH_CRUISE_TASK];
				ptCapPtzWatchOn->tWatchOn.tWaitTimeSec.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tWatchOnInfo.tWatchOnWaitTime.dwMinValue*60;
				ptCapPtzWatchOn->tWatchOn.tWaitTimeSec.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tWatchOnInfo.tWatchOnWaitTime.dwMaxValue*60;
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_PRESET)
            {
                TAppCltDevCapPtzPreset *ptCapPtzPreset = (TAppCltDevCapPtzPreset*)ptDevCap->pvCap;
                memset(ptCapPtzPreset,0,sizeof(TAppCltDevCapPtzPreset));
				ptCapPtzPreset->bPresetRemoveAllSup = TRUE;
				ptCapPtzPreset->tPresetRange.dwMin= g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMinValue;
				ptCapPtzPreset->tPresetRange.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPresetNum.dwMaxValue;
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_LIMIT)
            {
                TAppCltDevCapPtzLimit *ptCapPtzLimit = (TAppCltDevCapPtzLimit*)ptDevCap->pvCap;
                memset(ptCapPtzLimit,0,sizeof(TAppCltDevCapPtzLimit));
				ptCapPtzLimit->bLimitVertiUpSup = TRUE;
				ptCapPtzLimit->bLimitVertiDown = TRUE;
				ptCapPtzLimit->bLimitVertiUpSup = TRUE;
				ptCapPtzLimit->bLimitHorizRemoveSup = TRUE;
				ptCapPtzLimit->bLimitVertiRemoveSup = TRUE;
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].bySupport = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].bySupport;
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].bySupport = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].bySupport;
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_LEFT] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_LEFT];
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_RIGHT] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_RIGHT];
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_CLEAR] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_KEYCTRL_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_CLEAR];
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_LEFT] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_LEFT];
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_RIGHT] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_RIGHT];
				ptCapPtzLimit->tPosLimitModeInf.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_CLEAR] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPosLimitModeInfo.atPosLimitModeList[NVR_CAP_POS_LIMIT_SCAN_MODE].abyPosLimitList[NVR_CAP_POS_LIMIT_TYPE_CLEAR];
			}
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_PATH_CRS)
            {
                TAppCltDevCapPtzPathCrs *ptCapPtzPathCrs = (TAppCltDevCapPtzPathCrs*)ptDevCap->pvCap;
                memset(ptCapPtzPathCrs,0,sizeof(TAppCltDevCapPtzPathCrs));
				ptCapPtzPathCrs->bPathCruiseRemoveAllSup = TRUE;
				ptCapPtzPathCrs->tPathCruisePointNum.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCruiseInfo.tPathCruisePointNum.dwMinValue;
				ptCapPtzPathCrs->tPathCruisePointNum.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCruiseInfo.tPathCruisePointNum.dwMaxValue;
				ptCapPtzPathCrs->tPathCrsRange.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCrsNum.dwMinValue;
				ptCapPtzPathCrs->tPathCrsRange.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCrsNum.dwMaxValue;
				ptCapPtzPathCrs->tPathCrsStayTimeRange.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCruiseInfo.tPresetStayTime.dwMinValue;
				ptCapPtzPathCrs->tPathCrsStayTimeRange.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tPathCruiseInfo.tPresetStayTime.dwMaxValue;
		  
			}
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_SYNC_SCAN)
            {
                TAppCltDevCapPtzSyncScan *ptCapPtzSyncScan = (TAppCltDevCapPtzSyncScan*)ptDevCap->pvCap;
                memset(ptCapPtzSyncScan,0,sizeof(TAppCltDevCapPtzSyncScan));
				ptCapPtzSyncScan->bSyncScanRemoveAllSup = TRUE;
				ptCapPtzSyncScan->tSyncScanRange.dwMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tSyncScanNum.dwMinValue;
				ptCapPtzSyncScan->tSyncScanRange.dwMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tSyncScanNum.dwMaxValue;
			}
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_TIMING_TASK)
            {
                TAppCltDevCapPtzTimingTask *ptCapPtzTimingTask = (TAppCltDevCapPtzTimingTask*)ptDevCap->pvCap;
                memset(ptCapPtzTimingTask,0,sizeof(TAppCltDevCapPtzTimingTask));
				ptCapPtzTimingTask->tTimingTaskInfo.byMaxTimePeriod = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.byMaxTimePeriod;
				ptCapPtzTimingTask->tTimingTaskInfo.tResumeTime.dwMinValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.tResumeTime.dwMinValue;
				ptCapPtzTimingTask->tTimingTaskInfo.tResumeTime.dwMaxValue = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.tResumeTime.dwMaxValue;
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_TASK_MODE_NONE] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_TASK_MODE_NONE];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_HORIZON_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_HORIZON_SCAN_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_VERTICAL_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_VERTICAL_SCAN_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_PRESET_LOAD_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_PRESET_LOAD_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_PATH_CRUISE_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_PATH_CRUISE_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_FRAME_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_FRAME_SCAN_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_RAND_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_RAND_SCAN_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_FULLVIEW_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_FULLVIEW_SCAN_TASK];
				ptCapPtzTimingTask->tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_SYNC_SCAN_TASK] = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTimingTaskInfo.abyTmingTaskList[NVR_PTZCTRL_SYNC_SCAN_TASK];
			}
#ifdef _QCOM_
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
                TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;

                for(i=0;i<NVR_MAX_RES_TYPE_NUM;i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_SVAC].atResCaps[i].tFpsRange.dwMax = 25;
                }

                if(ptDevCap->dwDevID == 1 && g_bIsHost == TRUE)
                {
                    LcamIspGetParam(0, &tIspParam);

                    for(i=0;i<NVR_MAX_RES_TYPE_NUM;i++)
                    {
                        if(NVR_ISP_FREQUENCY_MODE_50HZ == tIspParam.eFrequencyMode)
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 50;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 50;
                        }
                        else
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 60;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 60;
                        }
                    }
                }

            }
#endif
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_HW)
            {
                TAppCltDevHWCap *ptCapHW = (TAppCltDevHWCap*)ptDevCap->pvCap;

                if(g_bIsHost != TRUE)
                {
                    ptCapHW->wAudDecNum = 0;//音频解码能力
                }
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_PTZ_COORDINATE)
			{
				TAppCltDevCapPtzCoo *ptCapPtzCoo = (TAppCltDevCapPtzCoo*)ptDevCap->pvCap;

                memset(ptCapPtzCoo,0,sizeof(TAppCltDevCapPtzCoo));
				ptCapPtzCoo->bBasicPosSupport = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byBasicPosSupport;
				ptCapPtzCoo->bSetPosSupport = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.bySetPosSupport;
				ptCapPtzCoo->bPointToNorth = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzParam.byPointToNorth;
				ptCapPtzCoo->tHorizAngleRange.nMin = 0;
				ptCapPtzCoo->tHorizAngleRange.nMax = 36000;
				ptCapPtzCoo->tVertiAngleRange.nMin = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTAngleRange.dwMinValue>9000 ? g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTAngleRange.dwMinValue-36000:g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTAngleRange.dwMinValue;
				ptCapPtzCoo->tVertiAngleRange.nMax = g_tNvrFixInterCap.tFixPtzInternalCap.tFixPtzAllInfoCap.tCapPtzInfo.tTAngleRange.dwMaxValue;
			}
#if (defined _HIS3516DV300_)
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
                u32 dwResCapNum = 0;
                TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;
                TAppCltDevResCap    atFixResCaps[APP_CLT_SUPPORTED_RES_MAX_NUM] = {0};
                TLcamMcWorkMode tWorkMode;

                LcamMcGetWorkModeCfgChn(0,&tWorkMode);

                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_SVAC].atResCaps[i].tFpsRange.dwMax = 25;

                }
                ///<MPEG格式下，最大分辨率1920*1080，帧率15fps
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth <= 1920)
                    {
                        atFixResCaps[i].dwResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth;
                        atFixResCaps[i].dwResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight;
                        dwResCapNum++;
                    }
                }
                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum = dwResCapNum;
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth = atFixResCaps[i].dwResWidth;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight = atFixResCaps[i].dwResHeight;

                }

                LcamIspGetParam(0, &tIspParam);
                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    if(NVR_ISP_FREQUENCY_MODE_50HZ == tIspParam.eFrequencyMode)
                    {
                        if (NVR_WORK_MODE_60FPS == tWorkMode.eWorkMode)
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 50;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 50;
                        }
                        else
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;
                        }
                    }
                    else
                    {
                        if(NVR_WORK_MODE_60FPS == tWorkMode.eWorkMode)
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 60;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 60;
                        }
                        else
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
                        }
                    }
                }

                if (NVR_PLAT_HIS3516DV300 == g_tNvrFixInterCap.tFixHwInternalCap.byPlat)
                {
                    TLcamMcVidEncParam tVidEncParam;
                    wChnId = ptDevCap->dwDevID - 1;

                    if (NVR_WORK_MODE_3STREAM == tWorkMode.eWorkMode)
                    {
                        mzero(tVidEncParam);

                        LcamMcGetVidEncParam(wChnId, ptDevCap->wEncID, &tVidEncParam);

                        if(0 == ptDevCap->wEncID || 1 == ptDevCap->wEncID)
                        {
                            dwResCapNum = 0;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1920;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 1080;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1920;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 1080;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 960;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 960;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 720;
                            dwResCapNum++;
                            if(1 == ptDevCap->wEncID)
                            {
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 720;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 720;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                                dwResCapNum++;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 704;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 704;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                                dwResCapNum++;
                            }
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].dwResCapNum = dwResCapNum;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].dwResCapNum = dwResCapNum;
                        }
                        else
                        {
                            dwResCapNum = 0;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 704;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 704;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 640;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 480;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 640;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 480;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 352;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 288;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 352;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 288;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].dwResCapNum = dwResCapNum;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].dwResCapNum = dwResCapNum;
                        }

                        if (tVidEncParam.wResWidth > ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[0].dwResWidth)
                        {
                            PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CAP_GET change res %d --> %d\n", tVidEncParam.wResWidth, ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[0].dwResWidth);

                            tVidEncParam.wResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[0].dwResWidth;
                            tVidEncParam.wResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[0].dwResHeight;
                            LcamMcSetVidEncParam(wChnId, ptDevCap->wEncID, &tVidEncParam);
                        }
                    }
                }
            }
#endif
#if (defined _HIS3516AV200_) || (defined _CV2X_)
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
			   TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;
			   wChnId = ptDevCap->dwDevID - 1;
#ifdef  _CV2X_
				u32 dwResCapNum = 0;
				TAppCltDevResCap	atFixResCaps[APP_CLT_SUPPORTED_RES_MAX_NUM] = {0};

				///<MPEG格式下，最大分辨率1920*1080
				for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
				{
					if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth <= 1920)
					{
						atFixResCaps[i].dwResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth;
						atFixResCaps[i].dwResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight;
						dwResCapNum++;
					}
				}
				ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum = dwResCapNum;
				for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
				{
					ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth = atFixResCaps[i].dwResWidth;
					ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight = atFixResCaps[i].dwResHeight;
					
				}              
#endif 
			   for(i=0;i<NVR_MAX_RES_TYPE_NUM;i++)
			   {
				   ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
			   }

			   LcamIspGetParam(wChnId, &tIspParam);

			   for(i=0;i<NVR_MAX_RES_TYPE_NUM;i++)
			   {
				   if(NVR_ISP_FREQUENCY_MODE_50HZ == tIspParam.eFrequencyMode)
				   {
					   ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
					   ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;
				   }
				   else
				   {
					   ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
					   ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
				   }
			   }

		   }
#endif

#ifdef _HIS3559A_
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
				u32 dwResCapNum = 0;
                TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;
				TAppCltDevResCap	atFixResCaps[APP_CLT_SUPPORTED_RES_MAX_NUM] = {0};
                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_SVAC].atResCaps[i].tFpsRange.dwMax = 25;
					
                }
				///<MPEG格式下，最大分辨率1920*1080，帧率15fps
				for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
				{
					if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth <= 1920)
					{
						atFixResCaps[i].dwResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth;
						atFixResCaps[i].dwResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight;
						dwResCapNum++;
					}
				}
				ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum = dwResCapNum;
				for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
				{
					ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth = atFixResCaps[i].dwResWidth;
					ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight = atFixResCaps[i].dwResHeight;
				}
                LcamIspGetParam(0, &tIspParam);

                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    if(NVR_ISP_FREQUENCY_MODE_50HZ == tIspParam.eFrequencyMode)
                    {
                    	if(0 == ptDevCap->wEncID)
                		{               				
	                    	if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].dwResWidth < 2048 ||  ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].dwResWidth < 2048)
	                    	{
	                        	ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 50;
	                        	ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 50;
	                    	}
							else
							{
								ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
	                        	ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;
							
	                		}
                    	}
						else
						{
							ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
	                        ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;
						}
						
					
                    }
                    else
                    {
                    	if(0 == ptDevCap->wEncID)
                    	{
							if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].dwResWidth < 2048 ||  ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].dwResWidth < 2048)
	                        {
		                        ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 60;
		                        ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 60;
							}
							else
							{
								ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
		                        ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
							}
                    	}
						else
						{
							ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
	                        ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
						}
                    }
                }

            }
#endif

#ifdef _SSC339G_
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
                u32 dwResCapNum = 0;
                u32 dwCapRes = 0;
                TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;
                TAppCltDevResCap    atFixResCaps[APP_CLT_SUPPORTED_RES_MAX_NUM] = {0};
                TLcamMcWorkMode tWorkMode;
                TNvrCapLcam tCapLcam;
                TLcamMcVidEncParam tVidEncParam;
                wChnId = ptDevCap->dwDevID - 1;
                mzero(tCapLcam);
                mzero(tVidEncParam);

                NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);
                LcamMcGetVidEncParam(wChnId, ptDevCap->wEncID, &tVidEncParam);
                dwCapRes = tCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wWidth * tCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wHeight;

                LcamMcGetWorkModeCfgChn(0,&tWorkMode);
                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_SVAC].atResCaps[i].tFpsRange.dwMax = 25;

                }
                ///<MPEG格式下，最大分辨率1920*1080，帧率15fps
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth <= 1920)
                    {
                        atFixResCaps[i].dwResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth;
                        atFixResCaps[i].dwResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight;
                        dwResCapNum++;
                    }
                }
                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum = dwResCapNum;
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth = atFixResCaps[i].dwResWidth;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight = atFixResCaps[i].dwResHeight;

                }

                if (NVR_WORK_MODE_3STREAM == tWorkMode.eWorkMode)
                {
                    if (1 == g_tNvrFixInterCap.tFixHwInternalCap.byExtPlat)
                    {

                    }
                    else if (NVR_CAP_NONSUPPORT == g_tNvrFixInterCap.tFixHwInternalCap.bySupTxtCap)
                    {
                        if(0 == ptDevCap->wEncID || 1 == ptDevCap->wEncID)
                        {
                            dwResCapNum = 0;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1920;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 1080;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1920;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 1080;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 960;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 960;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 1280;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 720;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].dwResCapNum = dwResCapNum;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].dwResCapNum = dwResCapNum;
                        }
                        else
                        {
                            dwResCapNum = 0;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 720;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 704;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 576;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 704;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 576;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 640;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 480;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 640;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 480;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResWidth = 352;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[dwResCapNum].dwResHeight = 288;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResWidth = 352;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[dwResCapNum].dwResHeight = 288;
                            dwResCapNum++;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].dwResCapNum = dwResCapNum;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].dwResCapNum = dwResCapNum;
                        }
                    }
                }
                else if (NVR_WORK_MODE_HDR == tWorkMode.eWorkMode)
                {
                    TNvrSysDevInfo tNvrSysDevInfo;
                    TAppCltDevCfgIsp tDevIspParam;
                    mzero(tNvrSysDevInfo);
                    mzero(tDevIspParam);

                    NvrSysGetDevInfo(&tNvrSysDevInfo);
                    if((tNvrSysDevInfo.dwPid <= 0x1818 && tNvrSysDevInfo.dwPid >= 0x1813) || tNvrSysDevInfo.dwPid == 0x1857)
                    {
                        //IPC2833切换至超宽动态模式时不支持畸变矫正(同时开启会导致帧率不足)
                        tDevIspParam.bHasLdc = TRUE;
                        for(i = 0; i < NVR_MAX_LCAM_CHN_NUM; i++)
                        {
                            NvrPuiGetDevParam(wChnId, i, NVR_PUI_ISP_PARAM, &tDevIspParam);
                            if(tDevIspParam.tLdc.eSwitch != NVR_ISP_VID_LDC_CLOSE)
                            {
                                tDevIspParam.tLdc.eSwitch = NVR_ISP_VID_LDC_CLOSE;
                                NvrPuiSetDevParam(wChnId, i, NVR_PUI_ISP_PARAM, &tDevIspParam);
                            }
                            tCapLcam.tLcamIspInfo.atLcamIsp[i].tVidLdc.bSupport = NVR_CAP_NONSUPPORT;
                        }
                    }
                }

                PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CAP_GET before type:%d 222\n", ptDevCap->eType);
                LcamIspGetParam(0, &tIspParam);
                PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CAP_GET get type:%d 222\n", ptDevCap->eType);
                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    if(NVR_ISP_FREQUENCY_MODE_50HZ == tIspParam.eFrequencyMode)
                    {
                        if(0 == ptDevCap->wEncID)
                        {
                            if (NVR_WORK_MODE_60FPS == tWorkMode.eWorkMode)
                            {
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 50;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 50;
                            }
                            else
                            {
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;

                            }
                        }
                        else
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 25;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 25;
                        }
                    }
                    else
                    {
                        if(0 == ptDevCap->wEncID)
                        {
                            if(NVR_WORK_MODE_60FPS == tWorkMode.eWorkMode)
                            {
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 60;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 60;
                            }
                            else
                            {
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
                                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
                            }
                        }
                        else
                        {
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 30;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 30;
                        }
                    }
                    if(NVR_PLAT_SSC339G == tHwCapInfo.ePlatType && NVR_WORK_MODE_HDR == tWorkMode.eWorkMode)
                    {
                        if(dwCapRes >= 6000000)
                        {
                            ///<ssc339g芯片性能问题,超宽动态模式下600w以上分辨率采集帧率不能超过20帧
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H264].atResCaps[i].tFpsRange.dwMax = 20;
                            ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_H265].atResCaps[i].tFpsRange.dwMax = 20;
                            tVidEncParam.byFrameRate = tVidEncParam.byFrameRate > 20 ? 20 : tVidEncParam.byFrameRate;
                        }
                    }
                }
                PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CAP_GET get type:%d 4444\n", ptDevCap->eType);
                LcamMcGetVidEncParam(wChnId, ptDevCap->wEncID, &tVidEncParam);
                NvrCapSetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);
            }
#endif

#ifdef _AX603A_
            else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_VID_ENC)
            {
                u32 dwResCapNum = 0;
                TAppCltDevCapVidEnc *ptCapVidEnc = (TAppCltDevCapVidEnc*)ptDevCap->pvCap;
                TAppCltDevResCap    atFixResCaps[APP_CLT_SUPPORTED_RES_MAX_NUM] = {0};
                for(i=0;i<APP_CLT_SUPPORTED_RES_MAX_NUM;i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].tFpsRange.dwMax = 15;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_SVAC].atResCaps[i].tFpsRange.dwMax = 25;

                }
                ///<MPEG格式下，最大分辨率1920*1080，帧率15fps
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    if(ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth <= 1920)
                    {
                        atFixResCaps[i].dwResWidth = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth;
                        atFixResCaps[i].dwResHeight = ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight;
                        dwResCapNum++;
                    }
                }
                ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum = dwResCapNum;
                for(i = 0; i < ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].dwResCapNum; i++)
                {
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResWidth = atFixResCaps[i].dwResWidth;
                    ptCapVidEnc->atEncTypeCap[NVR_VID_TYPE_MJPEG].atResCaps[i].dwResHeight = atFixResCaps[i].dwResHeight;

                }
            }
#endif
            else if (ptDevCap->eType == APP_CLT_DEV_CAP_LIFE_STAT)
            {
                TAppCltDevCapLifeStat *ptLifeCap = (TAppCltDevCapLifeStat*)ptDevCap->pvCap;

                s32 nChnId = ptDevCap->dwDevID - 1;
                if (nChnId < NVR_MAX_LCAM_CHN_NUM)
                {
                    ptLifeCap->bSup = g_aLifeStat[nChnId].wVidSrcID > NVR_MAX_LCAM_CHN_NUM ? FALSE : TRUE;
                }
            }
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_CFG_DUAL_OVERLAY)
			{	
				TAppCltDevCapDualChnOverlay *ptOverlayCap = (TAppCltDevCapDualChnOverlay*)ptDevCap->pvCap;

				memcpy(ptOverlayCap,&g_tNvrFixInterCap.tFixOverlayCap,sizeof(TAppCltDevCapDualChnOverlay));
			}
			else if(ptDevCap->eType == APP_CLT_DEV_CAP_BASIC_INTEL_THERMEAS_TEMP)
			{	
				TAppCltDevCapTherMeas *ptTherMeas = (TAppCltDevCapTherMeas*)ptDevCap->pvCap;

				memcpy(ptTherMeas,&g_tNvrFixInterCap.tFixTherMeasCap,sizeof(TAppCltDevCapTherMeas));
			}
            else if(APP_CLT_DEV_CAP_PROFE_INTEL == ptDevCap->eType )
            {
                TAppCltDevCapProfeIntel *ptProfeIntelCap = (TAppCltDevCapProfeIntel*)ptDevCap->pvCap;
                s32 nChnId = ptDevCap->dwDevID - 1;
                PRINTDBG(" get APP_CLT_DEV_CAP_PROFE_INTEL chn: %d\n", nChnId);

                if (nChnId < NVR_MAX_LCAM_CHN_NUM)
                {
                    ptProfeIntelCap->tCap.bySupSmoekeFire = g_tNvrFixInterCap.tFixProIntelCap.bySupSmokeFire[nChnId];
                    ptProfeIntelCap->tCap.bySupThermeasTemp = g_tNvrFixInterCap.tFixProIntelCap.bySupThermeasTemp;
                    ptProfeIntelCap->tCap.bySupAiTracking = g_tNvrFixInterCap.tFixProIntelCap.bySupAiTracking;

                    PRINTDBG(" ------sf:%d tt:%d at:%d\n", ptProfeIntelCap->tCap.bySupSmoekeFire, ptProfeIntelCap->tCap.bySupThermeasTemp, ptProfeIntelCap->tCap.bySupAiTracking);
                }
            }
            else if(APP_CLT_DEV_CAP_PROFE_INTEL_SMOKE_FIRE == ptDevCap->eType)
            {
                TAppCltDevCapSmokeFire *ptSmokeFire = (TAppCltDevCapSmokeFire*)ptDevCap->pvCap;

                memcpy(&ptSmokeFire->tCap, &g_tNvrFixInterCap.tFixProIntelCap.tFixSmokeFireCap, sizeof(TNvrCapSmokeFire));
            }
        }
        break;
    case LCAM_SPE_CTRL_TYPE_DEV_CFG_SET:
        {
            TNvrLcamSpecialDevCfg *ptDevCfg = (TNvrLcamSpecialDevCfg*)pParam;

			
			PRINTDBG("LCAM_SPE_CTRL_TYPE_DEV_CFG_SET eDevCfg %u\n",ptDevCfg->eType);
			
			if(APP_CLT_DEV_CFG_PTZ_BASE_INF == ptDevCfg->eType )
			{
				TNvrPtzBasicState tIpcPtzState;
				TNvrBasicPosInfo tBasicPos;
				//u32 dwHangle = 0;
				//u32 dwVangle = 0;
				mzero(tIpcPtzState);
				mzero(tBasicPos);
				NvrFixCfgGetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
				
				TAppCltDevCfgPtzBaseInf* ptAppCltDevCfgPtzBaseInf = (TAppCltDevCfgPtzBaseInf*)ptDevCfg->pvDevCfgArr;

				///<根据配置，设置相应参数给云台
				if(tIpcPtzState.dwAutoFlip != ptAppCltDevCfgPtzBaseInf->tCfg.dwAutoFlip)
				{
					mzero(tPtzCtrlInfo);
					wChnId = ptDevCfg->dwDevID - 1;
					tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_AUTOFLIP_SET;
					if (NVR_PTZ_MODE_ON == ptAppCltDevCfgPtzBaseInf->tCfg.dwAutoFlip)
					{
						if (NVR_PTZ_MODE_CLOSE == tIpcPtzState.dwManuLimitPos)
						{
							tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
							NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
						}
					}
					else
					{
						tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
						NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
					}
					OsApi_Delay(100);
				}
				if(tIpcPtzState.dwDepthrateSpd != ptAppCltDevCfgPtzBaseInf->tCfg.dwDepthrateSpd)
				{
					mzero(tPtzCtrlInfo);
					wChnId = ptDevCfg->dwDevID - 1;
					tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_DEPTHRATESPEED_SET;
					if (NVR_PTZ_MODE_ON == ptAppCltDevCfgPtzBaseInf->tCfg.dwDepthrateSpd)
					{
						tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
					}
					else
					{
						tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
					}
					NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
					OsApi_Delay(100);
				}
				if(tIpcPtzState.dwScanSpeedValue != ptAppCltDevCfgPtzBaseInf->tCfg.dwScanSpeedValue)
				{
					mzero(tPtzCtrlInfo);
					wChnId = ptDevCfg->dwDevID - 1;
					tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_HORIZONSCAN_SPEEDSET;
					tPtzCtrlInfo.wPanSpeed = ptAppCltDevCfgPtzBaseInf->tCfg.dwScanSpeedValue;
					NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
					OsApi_Delay(100);
				}
				if(tIpcPtzState.dwPreSetSpdValue != ptAppCltDevCfgPtzBaseInf->tCfg.dwPreSetSpdValue)	
				{
					mzero(tPtzCtrlInfo);
					wChnId = ptDevCfg->dwDevID - 1;
					tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PRESET_SPEED_SET;
					tPtzCtrlInfo.dwRes = ptAppCltDevCfgPtzBaseInf->tCfg.dwPreSetSpdValue;
					NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
					OsApi_Delay(100);
				}
				if(tIpcPtzState.eZoomSpeedValue != ptAppCltDevCfgPtzBaseInf->tCfg.eZoomSpeedValue)	
				{
					mzero(tPtzCtrlInfo);
					wChnId = ptDevCfg->dwDevID - 1;
					tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_SET_ZOOM_SPEED;
					tPtzCtrlInfo.dwRes = ptAppCltDevCfgPtzBaseInf->tCfg.eZoomSpeedValue;
					NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
				}
				ENvrDevPTOsdMode eCurrentPtOsdMode = NVR_DEV_PT_MODE_POINTER;
				eCurrentPtOsdMode = tIpcPtzState.ePTOsdMode;
				memcpy(&tIpcPtzState,&ptAppCltDevCfgPtzBaseInf->tCfg,sizeof(TNvrPtzBasicState));
				NvrFixCfgSetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);	
				if(eCurrentPtOsdMode != ptAppCltDevCfgPtzBaseInf->tCfg.ePTOsdMode)
				{
				    NvrFixDevOsdInfoAdjust();
				    NvrFixDevNotifyUpdatePtzOsd();
				}
				
			}
			else if (APP_CLT_DEV_CFG_PTZ_POWER_OFF_RSM == ptDevCfg->eType )
			{
				TNvrPowerOffRsmCfg tPowerOffRsmCfg;
				mzero(tPowerOffRsmCfg);
				TAppCltDevCfgPtzPowerOffRsm* ptAppCltDevCfgPtzPowerOffRsm = (TAppCltDevCfgPtzPowerOffRsm*)ptDevCfg->pvDevCfgArr;
				memcpy(&tPowerOffRsmCfg,&ptAppCltDevCfgPtzPowerOffRsm->tCfg,sizeof(TNvrPowerOffRsmCfg));
				NvrFixCfgSetParamByFlag(&tPowerOffRsmCfg,sizeof(TNvrPowerOffRsmCfg),NVR_PTZ_MODULE_POWE_RSM_PARAM);
			}
			else if(APP_CLT_DEV_CFG_PTZ_WATCHON == ptDevCfg->eType)
			{
				TNvrWatchOnParam tWatchOn;
				mzero(tWatchOn);
				TAppCltDevCfgPtzWatchOn* ptAppCltDevCfgPtzWatchOn = (TAppCltDevCfgPtzWatchOn*)ptDevCfg->pvDevCfgArr;

				tWatchOn.bEnable = ptAppCltDevCfgPtzWatchOn->bEnable;
				tWatchOn.dwPathCruiseID = ptAppCltDevCfgPtzWatchOn->dwPathCruiseID;
				tWatchOn.dwPresetID = ptAppCltDevCfgPtzWatchOn->dwPresetID;
				tWatchOn.dwWaitTimeSec = ptAppCltDevCfgPtzWatchOn->dwWaitTimeSec;
				tWatchOn.dwSyncScanID = ptAppCltDevCfgPtzWatchOn->dwSyncScanID;
				tWatchOn.byTaskType = NvrFixDevWatchOnTypeConvert2Lcam(ptAppCltDevCfgPtzWatchOn->eTaskType);
				NvrFixCfgSetParamByFlag(&tWatchOn,sizeof(TNvrWatchOnParam),NVR_PTZ_MODULE_WATCH_ON_PARAM);

				///<停止之前守望任务，重新开启
				NvrFixDevStopAllPtzTask();
				NvrFixDevStartAllPtzTask();

			}
            else if(APP_CLT_DEV_CFG_PTZ_AZIMUTH == ptDevCfg->eType)
            {
                NvrPtzDevSetBasicPos();
            }
			else if(APP_CLT_DEV_CFG_PTZ_PRESET == ptDevCfg->eType)
			{
				TNvrPresetInfo atPresetInfo[NVR_PUI_PRESET_MAX_NUM]={0};
				s8 achbuf[128] = {0};
				u32 dwUft8Len = sizeof(achbuf);
				TAppCltDevCfgPtzPreset* ptAppCltDevCfgPreset = (TAppCltDevCfgPtzPreset*)ptDevCfg->pvDevCfgArr;
				u16 wNum = ptAppCltDevCfgPreset->tCfg.wPresetNum;
				///<保存配置
				NvrFixCfgGetParamByFlag(atPresetInfo,NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo),NVR_PTZ_MODULE_PRESET_PARAM);
				memcpy(&atPresetInfo[wNum],&ptAppCltDevCfgPreset->tCfg.tPresetInfo,sizeof(TNvrPresetInfo));
				CharConvConvertUnicodetoUtf8(atPresetInfo[wNum].abyAlias,atPresetInfo[wNum].dwAliasLen,achbuf,dwUft8Len);
				atPresetInfo[wNum].dwAliasLen = dwUft8Len;
				memcpy(atPresetInfo[wNum].abyAlias,achbuf,dwUft8Len);
				NvrFixCfgSetParamByFlag(atPresetInfo,NVR_PUI_PRESET_MAX_NUM*sizeof(TNvrPresetInfo),NVR_PTZ_MODULE_PRESET_PARAM);

				///<发送ptz控制命令
				wChnId = ptDevCfg->dwDevID - 1;
				tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TYPE_PRESET_SAVE;
				tPtzCtrlInfo.wNumber = wNum;
				eRet = NvrFixDevPtzCtrl(wChnId,tPtzCtrlInfo);
			}
			else if(APP_CLT_DEV_CFG_PTZ_LIMIT == ptDevCfg->eType)
			{
				TNvrPtzBasicState tIpcPtzState;
				mzero(tIpcPtzState);
				NvrFixCfgGetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
				TAppCltDevCfgPtzLimit* ptAppCltDevCfgPtzLimit = (TAppCltDevCfgPtzLimit*)ptDevCfg->pvDevCfgArr;
				tIpcPtzState.dwManuLimitPos = ptAppCltDevCfgPtzLimit->tCfg.dwManuLimitPos;
				tIpcPtzState.dwScanLimitPos = ptAppCltDevCfgPtzLimit->tCfg.dwScanLimitPos;
				printf("set manlimit:"FORMAT_U32",scanlimit:"FORMAT_U32"\n",tIpcPtzState.dwManuLimitPos,tIpcPtzState.dwScanLimitPos);

				///<开启手动限位则关闭云台自动翻转
				mzero(tPtzCtrlInfo);
				wChnId = ptDevCfg->dwDevID - 1;
				tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_AUTOFLIP_SET;
				if (NVR_PTZ_MODE_ON == tIpcPtzState.dwManuLimitPos)
				{
					tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_CLOSE;
					NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
				}
				else
				{
					if (NVR_PTZ_MODE_ON == tIpcPtzState.dwAutoFlip) 	
					{
						tPtzCtrlInfo.eMode = NVR_PTZCTRL_MODE_OPEN;
						NvrFixDevPtzCtrl(wChnId, tPtzCtrlInfo);
					}
				}
				NvrFixCfgSetParamByFlag(&tIpcPtzState,sizeof(TNvrPtzBasicState),NVR_PTZ_MODULE_STATE_PARAM);
			}
			else if(APP_CLT_DEV_CFG_PTZ_PATH_CRS == ptDevCfg->eType)
			{
				TNvrPathCrsInfo atPathCrsInfo[NVR_MAX_PATHCRUISE_NUM] = {0};
				
				NvrFixCfgGetParamByFlag(&atPathCrsInfo,NVR_MAX_PATHCRUISE_NUM*sizeof(TNvrPathCrsInfo),NVR_PTZ_MODULE_PATH_CRS_PARAM);
				TAppCltDevCfgPtzPathCrs* ptAppCltDevCfgPathCrs = (TAppCltDevCfgPtzPathCrs*)ptDevCfg->pvDevCfgArr;
				memcpy(&atPathCrsInfo[ptAppCltDevCfgPathCrs->tCfg.byPathNum],&ptAppCltDevCfgPathCrs->tCfg.tPathCrsInfo,sizeof(TNvrPathCrsInfo));				
				NvrFixCfgSetParamByFlag(atPathCrsInfo,NVR_MAX_PATHCRUISE_NUM*sizeof(TNvrPathCrsInfo),NVR_PTZ_MODULE_PATH_CRS_PARAM);
			}
			else if(APP_CLT_DEV_CFG_PTZ_SYNC_SCAN == ptDevCfg->eType)
			{
				TNvrSyncScanInfo tSyncScanInfo;
				mzero(tSyncScanInfo);
				TAppCltDevCfgPtzSyncScan* ptAppCltDevCfgPtzSyncScan = (TAppCltDevCfgPtzSyncScan*)ptDevCfg->pvDevCfgArr;
				memcpy(&tSyncScanInfo,&ptAppCltDevCfgPtzSyncScan->tCfg,sizeof(TNvrSyncScanInfo));
				
				NvrFixCfgSetParamByFlag(&tSyncScanInfo,sizeof(TNvrSyncScanInfo),NVR_PTZ_MODULE_SYNS_SCAN_INFO_PARAM);
			}
			else if(APP_CLT_DEV_CFG_PTZ_TIMING_TASK == ptDevCfg->eType)
			{
				TNvrTmingTaskParam tTimeTask;
				mzero(tTimeTask);
				TAppCltDevCfgPtzTimingTask* ptAppCltDevCfgPtzTimingTask = (TAppCltDevCfgPtzTimingTask*)ptDevCfg->pvDevCfgArr;
				memcpy(&tTimeTask,&ptAppCltDevCfgPtzTimingTask->tCfg,sizeof(TNvrTmingTaskParam));
				NvrFixCfgSetParamByFlag(&tTimeTask,sizeof(TNvrTmingTaskParam),NVR_PTZ_MODULE_TIMING_TASK_PARAM);
			}
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_LIGHT_INTENSITY)
            {
            	TNvrCapLcam tNvrCapLcam;
				mzero(tNvrCapLcam);
				NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
                TAppCltDevCfgLightParam *ptAppCltDevCfgLightParam = (TAppCltDevCfgLightParam *)ptDevCfg->pvDevCfgArr;

                eRet = LcamIspGetParam(0, &tIspParam);
                if(NVR_ERR__OK != eRet)
                {
                    PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CFG_SET get isp param failed ret:%d\n", eRet);
                    return NVR_ERR__ERROR;
                }
				if(NVR_CAP_LCAMMC_PARAM_SPECIAL_339G2241 == tNvrCapLcam.tLcamMcInfo.eParamSpecial)
				{
					tIspParam.tInfrared.tWhiteLevel.dwInfaredLevel = ptAppCltDevCfgLightParam->dwLightIntensity;
				}
				else
				{
                	///<目前最大支持2组灯亮度设置
               		if(ptAppCltDevCfgLightParam->byLightId == 0)
                	{
                    	if(tIspParam.tInfrared.tInfaredLevel.bFarAndNearInfredCtrl)
                    	{
                        	tIspParam.tInfrared.tInfaredLevel.tFarAndNearLevel.dwNear = ptAppCltDevCfgLightParam->dwLightIntensity;
                    	}
                    	else
                    	{
                       	 tIspParam.tInfrared.tInfaredLevel.dwInfaredLevel = ptAppCltDevCfgLightParam->dwLightIntensity;
                    	}
                	}
                	else if(ptAppCltDevCfgLightParam->byLightId == 1)
                	{
                    	tIspParam.tInfrared.tInfaredLevel.tFarAndNearLevel.dwFar = ptAppCltDevCfgLightParam->dwLightIntensity;
                	}
				}
                eRet = LcamIspSetParam(0, &tIspParam);
                if(NVR_ERR__OK != eRet)
               	{
                    PRINTERR("LCAM_SPE_CTRL_TYPE_DEV_CFG_SET set isp param failed ret:%d\n", eRet);
                    return NVR_ERR__ERROR;
                }

            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_ZOOM_PARAM)
            {
                TAppCltDevCfgPtzZoomParam *ptAppCltDevCfgPtzZoomParam = (TAppCltDevCfgPtzZoomParam *)ptDevCfg->pvDevCfgArr;

				NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&ptAppCltDevCfgPtzZoomParam->nZmPos);

                ///<记录断电记忆zoom值
                g_tPowerOffMemCfg[0].nZoomPos = ptAppCltDevCfgPtzZoomParam->nZmPos;
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_PTZ_FOCUS_PARAM)
            {
                TAppCltDevCfgPtzFocusParam *ptAppCltDevCfgPtzFocusParam = (TAppCltDevCfgPtzFocusParam *)ptDevCfg->pvDevCfgArr;

                IspActionParam(0, ISP_ACT_SET_FOCUS_POSITION, &(ptAppCltDevCfgPtzFocusParam->nFcsPos));
            }
            else if(ptDevCfg->eType == APP_CLT_DEV_CFG_OSD) // 更新osd配置，需要更新ptz osd
            {
                NvrFixDevNotifyUpdatePtzOsd();
            }
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_ISP)
			{
				TAppCltDevCfgIsp *ptAppCltDevCfgIsp = (TAppCltDevCfgIsp *)ptDevCfg->pvDevCfgArr;

				NvrFixIspSetLcamCltParam(ptDevCfg->dwDevID-1,ptAppCltDevCfgIsp);			
			}
			else if (ptDevCfg->eType == APP_CLT_DEV_CFG_WORK_MODE)
			{
			    TAppCltDevCfgWorkMode *ptAppCltDevCfgWorkMode = (TAppCltDevCfgWorkMode *)ptDevCfg->pvDevCfgArr;
                TNvrCapLcam tNvrCapLcam;
                TLcamMcVidEncParam tDevVidEncCfg;
                u32 dwEncNum = 0;

                mzero(tNvrCapLcam);
                NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);

                wChnId = ptDevCfg->dwDevID - 1;

                LcamMcGetVidEncChn(wChnId, &dwEncNum);
                PRINTDBG("get chn %d vid enc num :"FORMAT_U32" cap %d\n", wChnId, dwEncNum, g_tNvrFixInterCap.tFixHwInternalCap.byLimitVidEncNum);
                if (dwEncNum > g_tNvrFixInterCap.tFixHwInternalCap.byLimitVidEncNum)
                {
                    dwEncNum = g_tNvrFixInterCap.tFixHwInternalCap.byLimitVidEncNum;
                    LcamMcSetVidEncChn(wChnId, dwEncNum);
                    PRINTDBG("work mode set  adwVidMutEncNum:"FORMAT_U32"\n", dwEncNum);
                }

                if (NVR_CAP_SUPPORT == tNvrCapLcam.tLcamMcInfo.tDevWorkMode[i].bMonitorModeSup)
                {
#ifdef _SSC339G_
                    //目前SSC339G不支持动态切换内存大小, 30kq支持
                    if(1 == g_tNvrFixInterCap.tFixHwInternalCap.byExtPlat)
                    {
                        if (APP_CLT_MONITOR_MODE_INTELLI == ptAppCltDevCfgWorkMode->eMonitorMode)
                        {
                            if (INTELLI_MODE_SYSMEM_SIZE != NvrFixCoreParseCmdlineSysMemInfo())
                            {
                                SysMemModify(INTELLI_MODE_SYSMEM_SIZE);
                            }
                        }
                        else
                        {
                            if (COMMON_MODE_SYSMEM_SIZE != NvrFixCoreParseCmdlineSysMemInfo())
                            {
                                SysMemModify(COMMON_MODE_SYSMEM_SIZE);
                            }
                        }
                    }
#endif
                }

#ifdef _SSC339G_
                if (NVR_CAP_NONSUPPORT == g_tNvrFixInterCap.tFixHwInternalCap.bySupTxtCap)
                {
                    if (APP_CLT_WORK_MODE_3STREAM == ptAppCltDevCfgWorkMode->eWorkMode)
                    {
                        mzero(tDevVidEncCfg);
                        LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);

                        if (tDevVidEncCfg.wResWidth > 1920)
                        {
                            tDevVidEncCfg.wResWidth = 1920;
                            tDevVidEncCfg.wResHeight = 1080;
                            LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);
                        }

                        mzero(tDevVidEncCfg);
                        LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
                        if (tDevVidEncCfg.wResWidth < 1280)
                        {
                            tDevVidEncCfg.wResWidth = 1920;
                            tDevVidEncCfg.wResHeight = 1080;
                        }
                        LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
                    }
                    else
                    {
                        mzero(tDevVidEncCfg);
                        LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
                        if (tDevVidEncCfg.wResWidth > 720)
                        {
                            tDevVidEncCfg.wResWidth = 720;
                            tDevVidEncCfg.wResHeight = 576;
                        }
                        LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
                    }
                }
#endif

#ifdef _CV2X_
				if(NVR_CAP_LCAMMC_PARAM_SPECIAL_IPC7201 == tNvrCapLcam.tLcamMcInfo.eParamSpecial)
				{
					if(APP_CLT_WORK_MODE_4K == ptAppCltDevCfgWorkMode->eWorkMode)
					{
						mzero(tDevVidEncCfg);
						LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);

						if (tDevVidEncCfg.wResWidth < 3840)
	                	{
	                		tDevVidEncCfg.wResWidth = 3840;
	                        tDevVidEncCfg.wResHeight = 2160;
							
							LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);
	                	}
						
						mzero(tNvrCapLcam);
						
						NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
						tDevVidEncCfg.wBitRate = 8*1024;
						LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);

						mzero(tDevVidEncCfg);
						
						LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
						if (tDevVidEncCfg.wResWidth > 1280)
	                	{
	                		tDevVidEncCfg.wResWidth = 1280;
	                        tDevVidEncCfg.wResHeight = 720;
							
							LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID,1, &tDevVidEncCfg);
	                	}

						mzero(tNvrCapLcam);
						
						NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
						tDevVidEncCfg.wBitRate = 4*1024;
						LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 1, &tDevVidEncCfg);
					
					}
					else
					{
						mzero(tDevVidEncCfg);
						
						LcamMcGetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);
						if (tDevVidEncCfg.wResWidth > 1920)
	                	{
	                		tDevVidEncCfg.wResWidth = 1920;
	                        tDevVidEncCfg.wResHeight = 1080;
							
							LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);
	                	}
						
						mzero(tNvrCapLcam);
						
						NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tNvrCapLcam);
						tDevVidEncCfg.wBitRate = 4*1024;
						LcamMcSetVidEncParam(ptAppCltDevCfgWorkMode->wVidSrcID, 0, &tDevVidEncCfg);
						
					}
				}
#endif
			}
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_DUAL_OVERLAY)
            {
                TAppCltDevCfgDualChnOverlay *ptAppCltChnOverlayParam = (TAppCltDevCfgDualChnOverlay *)ptDevCfg->pvDevCfgArr;
				TNvrFixCfg tFixCfg;
				TMediaCtrlDualChnOverlay tOverLay;

				NvrFixCfgGetParam(&tFixCfg);

				tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosSize = ptAppCltChnOverlayParam->eDualChnOverlayPosSize;
				tFixCfg.tMcCfg.tMediaDualInfo.eDualChnOverlayPosType = ptAppCltChnOverlayParam->eDualChnOverlayPosType;

				//<开启热成像叠加
				memset(&tOverLay, 0, sizeof(TMediaCtrlDualChnOverlay));
				tOverLay.bEnable = TRUE;
				tOverLay.dwDstEncChn = 0;
				tOverLay.dwSrcEncChn = 4;

				if(ptAppCltChnOverlayParam->eDualChnOverlayPosType == APP_CLT_THER_UP_LEFT_POS)
				{
					tOverLay.tRegionParam.wPosX = 0;
					tOverLay.tRegionParam.wPosY  =0;
				}

				if(ptAppCltChnOverlayParam->eDualChnOverlayPosType == APP_CLT_THER_UP_RIGHT_POS)
				{
					tOverLay.tRegionParam.wPosX = 1920-800;
					tOverLay.tRegionParam.wPosY  =0;
				}

				if(ptAppCltChnOverlayParam->eDualChnOverlayPosType == APP_CLT_THER_DOWN_LEFT_POS)
				{
					tOverLay.tRegionParam.wPosX = 0;
					tOverLay.tRegionParam.wPosY  =1080-600;
				}

				if(ptAppCltChnOverlayParam->eDualChnOverlayPosType == APP_CLT_THER_DOWN_RIGHT_POS)
				{
					tOverLay.tRegionParam.wPosX = 1920-800;
					tOverLay.tRegionParam.wPosY  =1080-600;
				}

				MediaCtrlSetDualChnOverlay(0, &tOverLay);

				NvrFixCfgSetParam(&tFixCfg);
            }
			else if(ptDevCfg->eType == APP_CLT_DEV_CFG_BASIC_INTEL_THERMEAS_TEMP)
            {
                TAppCltDevCfgTherMeas *ptAppCltTherMeasParam = (TAppCltDevCfgTherMeas *)ptDevCfg->pvDevCfgArr;
				TNvrFixCfg tFixCfg;
				NvrFixCfgGetParam(&tFixCfg);

				//若联动置为true，则只修改联动的配置
				if(ptAppCltTherMeasParam->tParam.bSetLinkCfg == TRUE)
				{
					tFixCfg.tIntelCfg.tTherMeasTemp.bSetLinkCfg = FALSE;
					memcpy(&tFixCfg.tIntelCfg.tTherMeasTemp.tLinkAlarm,&ptAppCltTherMeasParam->tParam.tLinkAlarm,sizeof(TNvrEventContact));
				}
				else
				{
					memcpy(&tFixCfg.tIntelCfg.tTherMeasTemp,&ptAppCltTherMeasParam->tParam,sizeof(TNvrIntelTherMeasTempParam));	
				    IpcDevSetXcoreParam(tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel]);
				}
				
				PRINTDBG("temp set cursel:%u %u %u\n",tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel,tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempFormat,tFixCfg.tIntelCfg.tTherMeasTemp.atRegionInfo[tFixCfg.tIntelCfg.tTherMeasTemp.dwCurSel].eTherTempType);
				NvrFixCfgSetParam(&tFixCfg);
            }
            else if(APP_CLT_DEV_CFG_PROFE_INTEL_SMOKE_FIRE == ptDevCfg->eType)
            {
                TAppCltDevCfgSmokkeFire *ptAppCltSmokeFireParam = (TAppCltDevCfgSmokkeFire *)ptDevCfg->pvDevCfgArr;
                TNvrIntelSmokeFireCfg   tSmokefire;             ///< 烟火识别参数
                s32 nPresetId = ptAppCltSmokeFireParam->tParam.tPlanInfo.nPresetId;
                wChnId = ptDevCfg->dwDevID - 1;

                NvrFixCfgGetParamById(wChnId, NVR_FIX_CFG_SMOKEFIRE, &tSmokefire, sizeof(tSmokefire));

                if (ptAppCltSmokeFireParam->tParam.bSetPlanCfg)
                {
                    tSmokefire.bEnable = ptAppCltSmokeFireParam->tParam.tPlanInfo.bEnable;
                    memcpy(&tSmokefire.atPreset[nPresetId], &ptAppCltSmokeFireParam->tParam.tPlanInfo, sizeof(TNvrIntelSmokeFirePlanInfo));
                    PRINTDBG("smoke fire %d set plan presetid :%d(0:all) enable:%d c:%d s:%d\n", wChnId,
                            ptAppCltSmokeFireParam->tParam.tPlanInfo.nPresetId, ptAppCltSmokeFireParam->tParam.tPlanInfo.bEnable,
                            ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion, ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion);
                    if (ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion || ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion)
                    {
                        s32 ii = 0;
                        for (ii = 0; ii < ptAppCltSmokeFireParam->tParam.tPlanInfo.wCheckRegion; ii++)
                        {
                            PRINTDBG("check indx:%d point num:%d\n", ii, ptAppCltSmokeFireParam->tParam.tPlanInfo.tCheckRegion[ii].dwPointNum);
                        }
                        for (ii = 0; ii < ptAppCltSmokeFireParam->tParam.tPlanInfo.wShieldRegion; ii++)
                        {
                            PRINTDBG("shield indx:%d point num:%d\n", ii, ptAppCltSmokeFireParam->tParam.tPlanInfo.tShieldRegion[ii].dwPointNum);
                        }
                    }
                }
                if (ptAppCltSmokeFireParam->tParam.bSetLinkCfg)
                {
                    memcpy(&tSmokefire.tLinkAlarm, &ptAppCltSmokeFireParam->tParam.tLinkAlarm, sizeof(TNvrEventContact));
                    PRINTDBG("smoke fire set link cfg :%d\n", tSmokefire.tLinkAlarm.byOsdShow);
                }

                if (ptAppCltSmokeFireParam->tParam.bSetOtherCfg)
                {
                    memcpy(&tSmokefire.tOtherInfo, &ptAppCltSmokeFireParam->tParam.tOtherInfo, sizeof(TNvrIntelSmokeFireOtherInfo));
                    PRINTDBG("smoke fire set other cfg :%d\n", tSmokefire.tOtherInfo.wSensity);
                }

                PRINTDBG("smoke fire set mini obj %d %d %d %d \n", tSmokefire.tOtherInfo.wX, tSmokefire.tOtherInfo.wY, tSmokefire.tOtherInfo.wWidth, tSmokefire.tOtherInfo.wHight);
                NvrFixCfgSetParamById(wChnId, NVR_FIX_CFG_SMOKEFIRE, &tSmokefire, sizeof(tSmokefire));

            }
            else if (APP_CLT_DEV_CFG_PROFE_INTEL_PREVENT_DETECT_LINK == ptDevCfg->eType )
            {
                TAppCltDevCfgAlarmLink *ptAppCltDevCfgPreventLinkParam = (TAppCltDevCfgAlarmLink *)ptDevCfg->pvDevCfgArr;
                TNvrAlarmProIntelLinkPattern tLinkPattern;
                mzero(tLinkPattern);
                memcpy(&ptAppCltDevCfgPreventLinkParam->tParam,&tLinkPattern,sizeof(TNvrAlarmProIntelLinkPattern));
            }
            else if( ( APP_CLT_DEV_CFG_BASIC_INTEL_CORDON <= ptDevCfg->eType
                    && APP_CLT_DEV_CFG_BASIC_INTEL_AUD_ABNORMAL >= ptDevCfg->eType)
                    || APP_CLT_DEV_CFG_MD == ptDevCfg->eType
                    || APP_CLT_DEV_CFG_BASIC_INTEL_FACE_DETECT == ptDevCfg->eType
                    || APP_CLT_DEV_CFG_OVERLAY == ptDevCfg->eType)
            {
                wChnId = ptDevCfg->dwDevID -1;
                PRINTDBG("chnid:%d intelli limit num %d\n", wChnId, g_tNvrFixInterCap.tFixHwInternalCap.byIntelliLimitNum);
                ///<   海外定制 基础智能限制
                if (g_tNvrFixInterCap.tFixHwInternalCap.byIntelliLimitNum)
                {
                    TLcamIntelCfg tLcamIntelCfg;

                    if (APP_CLT_DEV_CFG_BASIC_INTEL_VIRTUAL_FOCUS == ptDevCfg->eType)
                    {
                        TAppCltDevCfgVirtualFocus *ptAppCltVirtualFocus = (TAppCltDevCfgVirtualFocus *)ptDevCfg->pvDevCfgArr;
                        memcpy(&tLcamIntelCfg.atDeFocus[wChnId], &ptAppCltVirtualFocus->tParam, sizeof(TNvrIntelDeFocusParam));
                        PRINTDBG("APP_CLT_DEV_CFG_BASIC_INTEL_VIRTUAL_FOCUS %d\n", tLcamIntelCfg.atDeFocus[wChnId].tDeFocus.bEnable);
                        NvrCapSetBasicIntelliCap(wChnId, APP_CLT_DEV_CFG_BASIC_INTEL_VIRTUAL_FOCUS, tLcamIntelCfg.atDeFocus[wChnId].tDeFocus.bEnable);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_SCENE_CHANGE == ptDevCfg->eType)
                    {
                        TAppCltDevCfgSceneChange *ptAppCltSceneChange = (TAppCltDevCfgSceneChange *)ptDevCfg->pvDevCfgArr;
                        memcpy(&tLcamIntelCfg.atSceneChg[wChnId], &ptAppCltSceneChange->tParam, sizeof(TNvrIntelSceneChgParam));
                        PRINTDBG("APP_CLT_DEV_CFG_BASIC_INTEL_SCENE_CHANGE %d\n", tLcamIntelCfg.atSceneChg[wChnId].tSceneChg.bEnable);
                        NvrCapSetBasicIntelliCap(wChnId, APP_CLT_DEV_CFG_BASIC_INTEL_SCENE_CHANGE, tLcamIntelCfg.atSceneChg[wChnId].tSceneChg.bEnable);
                    }
                    else
                    {
                        BOOL bEnable = 0;
                        memcpy(&bEnable, ptDevCfg->pvDevCfgArr, sizeof(bEnable));
                        PRINTDBG("nomal %d %d\n", ptDevCfg->eType, bEnable);
                        NvrCapSetBasicIntelliCap(wChnId, ptDevCfg->eType, bEnable);
                    }
                }

                ///< 结构化算法处理基础智能配置参数
#ifdef _BASIC_INTELLI_      ///< NvrFixProfeIntelxxx接口需要编译时增加 nvrfixprofeintelli文件，目前只添加cv2x，后续各平台可相应添加
                do
                {
                    TNvrCapLcam tCapLcamInfo;
                    TLcamIntelCfg tLcamIntelCfg;

                    mzero(tLcamIntelCfg);

                    NvrCapGetCapParam(NVR_CAP_ID_LCAM, (void*)&tCapLcamInfo);
                    LcamIntelGetParam(&tLcamIntelCfg);

                    if (APP_CLT_DEV_CFG_BASIC_INTEL_CORDON == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelCordon.atLcamCapCordon[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_CORDON, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_AREA_INVASION == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelInvasion.atLcamCapInvasion[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_AREA_INVASION, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_AREA_ENTER == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelEnter.atLcamCapEnter[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_AREA_ENTER, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_AREA_LEAVE == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelLeave.atLcamCapLeave[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_AREA_LEAVE, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_OBJECT_PICK == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelPick.atLcamCapPick[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_OBJECT_PICK, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_OBJECT_LEFT == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelLeft.atLcamCapLeft[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_OBJECT_LEFT, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_PEOPLE_GATHER == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamPeopleGather.atLcamPeoleGather[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INEL_TYPE_PEOPLE_GATHER, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_SCENE_CHANGE == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelSceneChg.atLcamCapSceneChg[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_SCENE_CHANGE, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_VIRTUAL_FOCUS == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelVirtual.atLcamCapVirtual[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_VIRTUAL_FOCUS, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_MD == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamMcInfo.atMDCap[wChnId].bSupIntelliAlg)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_MD, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_OVERLAY == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamMcInfo.atOverLayCap[wChnId].bSupIntelliAlg)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_OVERLAY, &tLcamIntelCfg);
                    }
                    else if (APP_CLT_DEV_CFG_BASIC_INTEL_FACE_DETECT == ptDevCfg->eType && NVR_CAP_SUPPORT == tCapLcamInfo.tLcamBaiscIntel.tLcamIntelFaceDetect.atLcamCapFaceDetectInfo[wChnId].byIntelliAlgSupport)
                    {
                        NvrFixProfeIntelBasicIntelAlgSetParam(wChnId, NVR_INTEL_TYPE_FACE_DETECT, &tLcamIntelCfg);
                    }
                }  while (0);
#endif
            }

        }
        break;
    default:
        break;
    }

    if(eRet != NVR_ERR__OK)
    {
        PRINTERR("eType %u fail\n",eType);
    }
    
    return eRet;
}



void NvrFixSlaveReceiveHostMessageCB(char * pbyMsgBuf, u16 wBufLen, char * pbyMsgReturnBuf, u16 * pwReturnBufLen)
{
    u32 dwMsgType = 0;
    int nVersion = 1;
	u32 dwLdr = 0;
    char* pTemp;
    char *inner_ptr=NULL;
    u32 dwThreshold = 0;
    u32 dwDevId = 0;
    ISPDEV IspDev = 0;
    TNvrOnvifDevMsg tDev;
	u8 byLdrThr = 0;
	
    PRINTDBG("NvrFixSlaveReceiveHostMessageCB tRecvMsg: wBufLen:%u pbyMsgBuf:%s\n",wBufLen,pbyMsgBuf);
    *pwReturnBufLen = 0;

	pTemp = strtok_r(pbyMsgBuf, ";", &inner_ptr);
	if(NULL != pTemp)
	{
		if( 0 != strlen(pTemp))
		{
			nVersion = atoi(pTemp);
		}
	}

	pTemp = strtok_r(NULL,";",&inner_ptr);
	if(pTemp)
	{
		if( 0 != strlen(pTemp))
		{
			dwMsgType = atoi(pTemp);
		}
    }
    PRINTDBG("NvrFixSlaveReceiveHostMessageCB nVersion:%d,dwMsgType:%d\n",nVersion,dwMsgType);
    switch(dwMsgType)
    {
    	case NVR_PTZ_ONVIFTRANS_TYPE_SLAVE_LDR:
		{
			TAppCltDevCfgIsp tCltIspCfg = {0};
			mzero(tCltIspCfg);
			///ldr
			pTemp = strtok_r(NULL,";",&inner_ptr);
        	if(pTemp)
        	{
        		if( 0 != strlen(pTemp))
        		{
					dwThreshold = atoi(pTemp);
					PRINTDBG("NvrFixSlaveReceiveHostMessageCB dwThreshold:%lu\n",dwThreshold);
				}
        	}

            ///ldr
			pTemp = strtok_r(NULL,";",&inner_ptr);
        	if(pTemp)
        	{
        		if( 0 != strlen(pTemp))
        		{
					dwLdr = atoi(pTemp);
					PRINTDBG("NvrFixSlaveReceiveHostMessageCB dwLdr:%lu\n",dwLdr);
				}
        	}

#ifndef _QCOM_
        	void ExIspSetLdrParam(u16 wChnId, u32 dwThreshold, u32 dwLdr);
            ExIspSetLdrParam(0,dwThreshold,dwLdr);
            ExIspSetLdrParam(1,dwThreshold,dwLdr);
#endif
			
		}
		break;
        case NVR_PTZ_ONVIFTRANS_TYPE_DEV_GET:
            {
                ///dev
    			pTemp = strtok_r(NULL,";",&inner_ptr);
            	if(pTemp)
            	{
            		if( 0 != strlen(pTemp))
            		{
    					dwDevId = atoi(pTemp);
    					PRINTDBG("NvrFixSlaveReceiveHostMessageCB dwDevId:%lu\n",dwDevId);
    				}
            	}

                dwDevId -= 2;

                IspDev = (dwDevId == 0) ? 1 : 0;
                tDev.wPangle = g_tPowerOffMemCfg[dwDevId].wHorizonPos;
                tDev.wTangle = g_tPowerOffMemCfg[dwDevId].wVerticalPos;
                IspActionParam(IspDev, ISP_ACT_GET_ZOOM_POSITION, &tDev.dwZmPos);
                IspActionParam(IspDev, ISP_ACT_GET_FOCUS_POSITION, &tDev.dwFcsPos);

                NvrSlaveToHostMessage(2,0,(void *)&tDev);
                
                PRINTDBG("slave GET dwDevId:%u,wPangle:%u,dwZmPos:%lu,dwFcsPos %lu\n",dwDevId,tDev.wPangle,tDev.wTangle,tDev.dwZmPos,tDev.dwFcsPos);

            }
            break;
        case NVR_PTZ_ONVIFTRANS_TYPE_DEV_SET:
            {
                pTemp = strtok_r(NULL,";",&inner_ptr);
                tDev.byDevId = atoi(pTemp);
                pTemp = strtok_r(NULL,";",&inner_ptr);
                tDev.wPangle = atoi(pTemp);
                pTemp = strtok_r(NULL,";",&inner_ptr);
                tDev.wTangle = atoi(pTemp);
                pTemp = strtok_r(NULL,";",&inner_ptr);
                tDev.dwZmPos = atoi(pTemp);

                tDev.byDevId -= 2;

                IspDev = (tDev.byDevId == 0) ? 1 : 0;
				NvrFixIspSetKeyParam(IspDev,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&tDev.dwZmPos);

                TNvrPtzCtrlInfo tPtzCtrlInfo;
                tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PANPOSION_SET;
                tPtzCtrlInfo.wXposition = tDev.wPangle;
                NvrFixDevPtzCtrl(tDev.byDevId,tPtzCtrlInfo);

                sleep(1);
                
                tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TILTPOSION_SET;
                tPtzCtrlInfo.wYposition = tDev.wTangle;
                NvrFixDevPtzCtrl(tDev.byDevId,tPtzCtrlInfo);

                PRINTDBG("slave SET dwDevId:%u,wPangle:%u,dwZmPos:%lu,dwFcsPos %lu\n",tDev.byDevId,tDev.wPangle,tDev.wTangle,tDev.dwZmPos,tDev.dwFcsPos);
            }
            break;
		 case NVR_PTZ_ONVIFTRANS_TYPE_LDR_THR:
            {
                ///ldrthr
    			pTemp = strtok_r(NULL,";",&inner_ptr);
            	if(pTemp)
            	{
            		if( 0 != strlen(pTemp))
            		{
    					byLdrThr = atoi(pTemp);
    					PRINTDBG("NvrFixSlaveReceiveHostMessageCB byLdrThr:%u\n",byLdrThr);
    				}
            	}
				
				NvrFixSetDnThreshold(byLdrThr);
                
				PRINTDBG("NvrFixSlaveReceiveHostMessageCB byLdrThr2:%u\n",byLdrThr);

            }
            break;
		default:
			break;
}
}

NVRSTATUS NvrFixHostToSlaveMessage(ENvrOnvifTransDataType eReportDataType, void *pbyBuf)
{
	u32 dwSendLen = 0;
	u32 dwReceiveLen = 0;
	u32 dwDevId = 2;
	s8 achTransInBuff[IPC_CORE_TRANS_DATA_BUF_MAX_NUM+16]={0};
	s8 achTransOutBuff[IPC_CORE_TRANS_DATA_BUF_MAX_NUM+16]={0};
    u8 *pbyDevId = NULL;
    TNvrOnvifDevMsg *ptDev;
	u8 *pbyLdrThr = NULL;

    dwReceiveLen = sizeof(achTransOutBuff);
    
	switch(eReportDataType)
	{
		case NVR_PTZ_ONVIFTRANS_TYPE_SLAVE_LDR:
		{
			///通知从片光敏
			TIspLedInfo *ptIspIrInfo;
            
            ptIspIrInfo = (TIspLedInfo *)pbyBuf;
			
			snprintf(achTransInBuff,sizeof(achTransInBuff),"%d;%d;"FORMAT_U32";"FORMAT_U32"", 1, NVR_PTZ_ONVIFTRANS_TYPE_SLAVE_LDR, ptIspIrInfo->ledStr[0], ptIspIrInfo->ledStr[1]);
			dwSendLen = strlen(achTransInBuff);
			PRINTDBG("NvrFixHostToSlaveMessage dwDevId:%d,buf:%s,dwSendLen:%d!\n",dwDevId,achTransInBuff,dwSendLen);
			AppCltProductTransData(dwDevId,achTransInBuff,dwSendLen,achTransOutBuff,&dwReceiveLen);
    		}
            break;
        case NVR_PTZ_ONVIFTRANS_TYPE_DEV_GET:
            {
                pbyDevId = (u8 *)pbyBuf;
                
                snprintf(achTransInBuff,sizeof(achTransInBuff),"%d;%d;%d",1,NVR_PTZ_ONVIFTRANS_TYPE_DEV_GET,*pbyDevId);
                dwSendLen = strlen(achTransInBuff);
                PRINTDBG("NvrFixHostToSlaveMessage dwDevId:%d,buf:%s,dwSendLen:%d!\n",*pbyDevId,achTransInBuff,dwSendLen);
                AppCltProductTransData((u32)(*pbyDevId),achTransInBuff,dwSendLen,achTransOutBuff,&dwReceiveLen);

            }
            break;
         case NVR_PTZ_ONVIFTRANS_TYPE_DEV_SET:
            {
                ptDev = (TNvrOnvifDevMsg *)pbyBuf;
                
                snprintf(achTransInBuff,sizeof(achTransInBuff),"%d;%d;%d;%d;%d;%d",1,NVR_PTZ_ONVIFTRANS_TYPE_DEV_SET,ptDev->byDevId,ptDev->wPangle,ptDev->wTangle,(s32)ptDev->dwZmPos);
                dwSendLen = strlen(achTransInBuff);
                PRINTDBG("NvrFixHostToSlaveMessage dwDevId:%d,buf:%s,dwSendLen:%d!\n",ptDev->byDevId,achTransInBuff,dwSendLen);
                AppCltProductTransData((u32)(ptDev->byDevId),achTransInBuff,dwSendLen,achTransOutBuff,&dwReceiveLen);

            }
            break;
		 case NVR_PTZ_ONVIFTRANS_TYPE_LDR_THR:
		 	{				
                pbyLdrThr = (u8 *)pbyBuf;
                
                snprintf(achTransInBuff,sizeof(achTransInBuff),"%d;%d;%d",1,NVR_PTZ_ONVIFTRANS_TYPE_LDR_THR,*pbyLdrThr);
                dwSendLen = strlen(achTransInBuff);
                PRINTDBG("NvrFixHostToSlaveMessage pbyLdrThr:%d,buf:%s,dwSendLen:%d!\n",*pbyLdrThr,achTransInBuff,dwSendLen);
                AppCltProductTransData(dwDevId,achTransInBuff,dwSendLen,achTransOutBuff,&dwReceiveLen);
            }
		 	break;
		default:
		    break;
	}

	return NVR_ERR__OK;
}

void NvrFixHostReceiveSlaveMessageCB(u16 wChnId,ENvrAlarmType eAlarmType,u16 wAlarmNo, u8 byAlarmStatus, void *pData , void *pContext)
{
	s8* pchCmdTypeStr1 = "<CmdType>";
	s8* pchCmdTypeStr2 = "</CmdType>";
	s8 achCmdType[16]= {0};
	s8 achTmpBuff[128]= {0};
    u8 byLedId = 0;
    u8 byLedStr1 = 0;
    u8 byLedStr2 = 0;
	
    PRINTTMP("NvrPuiAlarmStatusCB wChnId:%d,eAlarmType:%d,wAlarmNo:%d,byAlarmStatus:%d,pData:%s\n", wChnId,eAlarmType,wAlarmNo,byAlarmStatus,pData);
	if(NVR_ALARM_TYPE_TRANSDATA == eAlarmType)
	{
		pchCmdTypeStr1 = strstr(pData,"<CmdType>");
		pchCmdTypeStr2 = strstr(pData,"</CmdType>");
		if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
		{
			memcpy(achCmdType,pchCmdTypeStr1+strlen("<CmdType>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<CmdType>"));
		}
		PRINTDBG("NvrPuiAlarmStatusCB achCmdType:%s\n", achCmdType);
		if(0 ==strcmp(achCmdType,"fraredledControl"))
		{
			pchCmdTypeStr1 = strstr(pData,"<LedId>");
			pchCmdTypeStr2 = strstr(pData,"</LedId>");
			
			if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
			{
				memcpy(achTmpBuff,pchCmdTypeStr1+strlen("<LedId>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<LedId>"));
			}
			byLedId = atoi(achTmpBuff);

			mzero(achTmpBuff);

            pchCmdTypeStr1 = strstr(pData,"<LedStr1>");
			pchCmdTypeStr2 = strstr(pData,"</LedStr1>");
			
			if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
			{
				memcpy(achTmpBuff,pchCmdTypeStr1+strlen("<LedStr1>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<LedStr1>"));
			}
			byLedStr1 = atoi(achTmpBuff);

			mzero(achTmpBuff);

            pchCmdTypeStr1 = strstr(pData,"<LedStr2>");
			pchCmdTypeStr2 = strstr(pData,"</LedStr2>");
			
			if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
			{
				memcpy(achTmpBuff,pchCmdTypeStr1+strlen("<LedStr2>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<LedStr2>"));
			}
			byLedStr2 = atoi(achTmpBuff);

			//1,2远光，0,3近光
			if(byLedId == 1)
			{
			     NvrFixLampCtrl(2,byLedStr1);
			     NvrFixLampCtrl(3,byLedStr2);
			}
			else
			{
			     NvrFixLampCtrl(0,byLedStr2);
			     NvrFixLampCtrl(1,byLedStr1);
			}

			PRINTDBG("NvrPuiAlarmStatusCB byLedId:%u,byLedStr1:%u,byLedStr2:%u\n", byLedId,byLedStr1,byLedStr2);
		}
        else if(0 ==strcmp(achCmdType,"ptzver"))
        {
			pchCmdTypeStr1 = strstr(pData,"<PtzVer>");
			pchCmdTypeStr2 = strstr(pData,"</PtzVer>");
			
			if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
			{
				memcpy(achTmpBuff,pchCmdTypeStr1+strlen("<PtzVer>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<PtzVer>"));
			}

            strcpy(g_achPtzVer,achTmpBuff);

            NvrSysSetClipVer(g_achPtzVer);

			PRINTDBG("NvrPuiAlarmStatusCB g_achPtzVer:%s\n", g_achPtzVer);
		}
        else if(0 ==strcmp(achCmdType,"fixdev"))
        {
            pchCmdTypeStr1 = strstr(pData,"<FixDev>");
            pchCmdTypeStr2 = strstr(pData,"</FixDev>");
            
            if(NULL != pchCmdTypeStr1&&NULL != pchCmdTypeStr2)
            {
                memcpy(achTmpBuff,pchCmdTypeStr1+strlen("<FixDev>"),pchCmdTypeStr2 -pchCmdTypeStr1-strlen("<FixDev>"));
            }

            sscanf(achTmpBuff, "%u-%u-"FORMAT_U32"-"FORMAT_U32"",\
                            (unsigned int *)&g_tOnvifDevMsg.wPangle,(unsigned int *)&g_tOnvifDevMsg.wTangle,&g_tOnvifDevMsg.dwZmPos,&g_tOnvifDevMsg.dwFcsPos);

                            
            PRINTDBG("NvrFixHostReceiveSlaveMessageCB %u-%u-%lu-%lu\n", g_tOnvifDevMsg.wPangle,g_tOnvifDevMsg.wTangle,g_tOnvifDevMsg.dwZmPos,g_tOnvifDevMsg.dwFcsPos);

            OsApi_SemGive(g_hHSCSem);
        }

	}

    return;
}

NVRSTATUS NvrSlaveToHostMessage(u8 byType,u8 byChnId,void *pbyBuf)
{
	TNvrAlarmSrc tAlarmSrc;
	TNvrBrokenDownTime tAlarmTime;
	s8 szData[1024] = {0};
    u8 byLedStr1 = 0;	
    u8 byLedStr2 = 0;
    s8 *pachPtzVer;//云台版本号
    TNvrOnvifDevMsg *ptDev;

    mzero(tAlarmSrc);
	mzero(tAlarmTime);
    
	tAlarmSrc.wDevId = 0;
	tAlarmSrc.byAlarmType = NVR_ALARM_TYPE_TRANSDATA;
	tAlarmSrc.byAlarmNum = 0;
	PFNvrAlarmStatusChangeCB pfAPICallBack = NULL;  

    switch(byType)
    {
    case 0:
        {
            byLedStr1 = *(u8 *)pbyBuf;
            byLedStr2 = *((u8 *)pbyBuf+sizeof(u8));
            PRINTDBG("NvrSlaveToHostMessage byId[%u],ledstr1 %u,ledstr2 %u\n",byChnId,byLedStr1,byLedStr2);
            
            snprintf(szData, sizeof(szData), "<Notify>"
                "<CmdType>fraredledControl</CmdType>"
                "<LedId>%d</LedId>"
                "<LedStr1>%d</LedStr1>"
                "<LedStr2>%d</LedStr2>"       
                "</Notify>",byChnId,byLedStr1,byLedStr2);
        }
        break;
    case 1:
        {
            pachPtzVer = (s8 *)pbyBuf;
            PRINTDBG("NvrSlaveToHostMessage pachPtzVer %s\n",pachPtzVer);

            snprintf(szData, sizeof(szData), "<Notify>"
                "<CmdType>ptzver</CmdType>"
                "<PtzVer>%s</PtzVer>"
                "</Notify>",pachPtzVer);
        }
        break;
    case 2:
        {
            ptDev = (TNvrOnvifDevMsg *)pbyBuf;
            PRINTDBG("NvrSlaveToHostMessage %u-%u-"FORMAT_U32"-"FORMAT_U32"\n",ptDev->wPangle,ptDev->wTangle,ptDev->dwZmPos,ptDev->dwFcsPos);

            snprintf(szData, sizeof(szData), "<Notify>"
                           "<CmdType>fixdev</CmdType>"
                           "<FixDev>%u-%u-"FORMAT_U32"-"FORMAT_U32"</FixDev>"
                           "</Notify>",ptDev->wPangle,ptDev->wTangle,ptDev->dwZmPos,ptDev->dwFcsPos);

        }
        break;
    default:
        break;
    }

	PRINTDBG("NvrSlaveToHostMessage send server pfAPICallBack:%p\n",g_pfNvrOnvirproAPICBkNotify);
	NvrAlarmGetSpecialCBAddress(&pfAPICallBack,NVR_APP_PROTO_ONVIF);
	g_pfNvrOnvirproAPICBkNotify = pfAPICallBack;
	
	if(NULL != g_pfNvrOnvirproAPICBkNotify)
	{
		g_pfNvrOnvirproAPICBkNotify(tAlarmSrc,TRUE,tAlarmTime,szData);
	}
	PRINTDBG("NvrSlaveToHostMessage send server pfAPICallBack:%p\n",g_pfNvrOnvirproAPICBkNotify);

    return NVR_ERR__OK;
}

void NvrFixLampCtrl(u8 byLedId, u8 byLedStr)
{	
	s32 nRet = 0;
    TNvrCapLcam tCapLcam;

    mzero(tCapLcam);

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);
    if ( !tCapLcam.tLcamIspInfo.atLcamIsp[0].tInfrared.bSupport)
    {
        PRINTTMP("NvrFixLampCtrl no sup ir \n");
        return;
    }
//安霸没有红外灯,设置会影响syserr.log打印
#ifndef __CV2X_
    TLensParams tLensParams;
    tLensParams.dwLensId = 0;
    tLensParams.adwVal32[0] =  byLedStr | (byLedId << 24) ;
	nRet = VidLensCtrl(0, LENS_CTRL_SET_IRLED, &tLensParams);

	PRINTTMP("NvrFixLampCtrl byLedId:%d,byLedStr %u,nRet:%d\n",byLedId,byLedStr,nRet);
#endif
}

void NvrIspSetInfrared(u8 byChnId,u8 *pbyLedStr)
{
    if(g_bIsHost == TRUE)
    {
        NvrFixLampCtrl(4,*pbyLedStr);
    }
    else
    {        
        NvrSlaveToHostMessage(0,byChnId,pbyLedStr);
    }
}

s32 NvrIspIrCb(void *pParam)
{
#ifndef _QCOM_
    TIspInfraredInfo *ptIspInfraredInfo = NULL;
    s32 i = 0;
    u32 dwIrValue = 0;
    ptIspInfraredInfo = (TIspInfraredInfo *)pParam;

    for (i = 0; i < ptIspInfraredInfo->infraredNum; i++)
   	{
        dwIrValue = ptIspInfraredInfo->infraredStr[i];
        NvrFixLampCtrl(i, (u8)dwIrValue);

        ///< 保留isp设置的红外灯值，用于做加热策略
        if (i < (sizeof(g_nInfraredVal)/sizeof(g_nInfraredVal[0])))
        {
            g_nInfraredVal[i] = dwIrValue;
        }
    }
	
#else
    TIspLedInfo *ptIspIrInfo = NULL;
    static TIspLedInfo tLastLdrInfo = {0};//光敏
    u8 achLedStr[2] = {0};

    ptIspIrInfo = (TIspLedInfo *)pParam;

    if(ptIspIrInfo->ispDev == 128)
    {
        if((0 == memcmp(&tLastLdrInfo, ptIspIrInfo,sizeof(TIspLedInfo))))
        {
            return 0;
        }
        PRINTDBG("NvrIspIrCb dev[%u],ledNum %u,threshold %u,ldr %u\n",ptIspIrInfo->ispDev,ptIspIrInfo->ledNum,ptIspIrInfo->ledStr[0],ptIspIrInfo->ledStr[1]);

        NvrFixHostToSlaveMessage(NVR_PTZ_ONVIFTRANS_TYPE_SLAVE_LDR,(void *)ptIspIrInfo);
        
        memcpy(&tLastLdrInfo,ptIspIrInfo,sizeof(TIspLedInfo));
    }
    else
    {   
         ///相同或者ispdev错误则返回
        if((ptIspIrInfo->ispDev >= NVR_MAX_LCAM_CHN_NUM) || (0 == memcmp(&g_tLastLedInfo[ptIspIrInfo->ispDev], ptIspIrInfo,sizeof(TIspLedInfo))))
        {
            return 0;
        }

        PRINTDBG("NvrIspIrCb dev[%u],ledNum %u,led1 %u,led2 %u\n",ptIspIrInfo->ispDev,ptIspIrInfo->ledNum,ptIspIrInfo->ledStr[0],ptIspIrInfo->ledStr[1]);

		achLedStr[0] = (u8)ptIspIrInfo->ledStr[0];
		achLedStr[1] = (u8)ptIspIrInfo->ledStr[1];	

        NvrIspSetInfrared((u8)ptIspIrInfo->ispDev,achLedStr);

        memcpy(&g_tLastLedInfo[ptIspIrInfo->ispDev],ptIspIrInfo,sizeof(TIspLedInfo));
    }
#endif

    
    return 0;
}

//如果是主机，则注册接收cb，解析红外亮度数据，同时也处理主机红外的回调，如果是从机，则处理从机红外的回调，并且把控灯信息发到主机
void NvrFixInitInfrared()
{
    TNvrCapLcam tCapLcamInfo;

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, (void*)&tCapLcamInfo);

    if(tCapLcamInfo.tLcamMcInfo.byLcamChnNum == 1 
        || NVR_CAP_LCAMMC_PARAM_SPECIAL_IPC7201 == tCapLcamInfo.tLcamMcInfo.eParamSpecial
        || NVR_CAP_LCAMMC_PARAM_SPECIAL_IPC528  == tCapLcamInfo.tLcamMcInfo.eParamSpecial
		|| NVR_CAP_LCAMMC_PARAM_SPECIAL_339G2241  == tCapLcamInfo.tLcamMcInfo.eParamSpecial)
    {
        g_bIsHost = TRUE;
    }
    
    if(g_bIsHost == TRUE)
    {
        PRINTDBG("NvrFixInitInfrared host\n");
        IspCbRegister(0, ISP_REGFUN_INFRARED_CHANGED, NvrIspIrCb);
		///<2241从机注册回调
		if(NVR_CAP_LCAMMC_PARAM_SPECIAL_339G2241  == tCapLcamInfo.tLcamMcInfo.eParamSpecial)
		{
			IspCbRegister(1, ISP_REGFUN_INFRARED_CHANGED, NvrIspIrCb);
		}
		NvrPuiAlarmStatusCallBack(NvrFixHostReceiveSlaveMessageCB);

        ///<创建主从通讯信号量
    	if(!OsApi_SemBCreate(&g_hHSCSem))
    	{
    		PRINTDBG("create g_hHSCSem failed\n");
    		return;
    	}
    }
    else
    {        
        PRINTDBG("NvrFixInitInfrared slave\n");
        IspCbRegister(0, ISP_REGFUN_INFRARED_CHANGED, NvrIspIrCb);
        IspCbRegister(1, ISP_REGFUN_INFRARED_CHANGED, NvrIspIrCb);
        ///<注册从片接收主片数据回调
		NvrProductRegTransDataDataCallBack(NvrFixSlaveReceiveHostMessageCB);
    }
    
}

s32 NvrFixDelayCb(HTIMERHANDLE dwTimerId, void* param)
{
    u8 byDevId = 0;
    static u8 byTime = 0;
    u8 achLedStr[2] = {0};

    if(byTime <= 10)
    {
    	achLedStr[0] = (u8)g_tLastLedInfo[byDevId].ledStr[0];
		achLedStr[1] = (u8)g_tLastLedInfo[byDevId].ledStr[1];	
		
        for(byDevId=0;byDevId<NVR_MAX_LCAM_CHN_NUM;byDevId++)
        {
            NvrIspSetInfrared((u8)byDevId,achLedStr);
        }

        if(byTime == 2)
        {
            NvrUserOpenSSH();
        }
        
    }

    PRINTDBG("NvrFixDelayCb\n");

    if(byTime <= 20)
    {
        NvrSlaveToHostMessage(1,0,(void *)g_achPtzVer);
        
        OsApi_TimerSet( g_hTimer, 5*1000, NvrFixDelayCb, NULL);//延时5s
    }

    byTime++;

    return 0;
}

NVRSTATUS NvrFixIspGetLDR(u32 *pdwValue)
{
	NVRSTATUS eRet = NVR_ERR__OK;
	THwmonStat tHwmonStat;
	memset(&tHwmonStat, 0, sizeof(tHwmonStat));

	//获取光敏阻值
	tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_BRIGHT, 0);
    eRet = NvrBrdApiHwmonGetStatus(&tHwmonStat);
	if (NVR_ERR__OK != eRet)
	{
		PRINTERR("get LDR failed\n");
		return NVR_ERR__ERROR;
	}

	*pdwValue = tHwmonStat.tEntry.tBright.dwCur ;//光敏阻值

	return NVR_ERR__OK;
}

s32 NvrFixIspLDRCb(void * pParam)
{
    NVRSTATUS eRet = NVR_ERR__OK;

    TIspLdrInfo tIspLdrInfo = {0};

    //传-1，-1表示isp内部通过底层接口自己获取阈值
    tIspLdrInfo.ldrDayToNightThr = -1;
    tIspLdrInfo.ldrNightToDay = -1;
    eRet = NvrFixIspGetLDR(&tIspLdrInfo.ldrValue);

    PRINTTMP("ldr:%d, night:%d, day:%d\n", tIspLdrInfo.ldrValue, tIspLdrInfo.ldrDayToNightThr, tIspLdrInfo.ldrNightToDay);
    if(NVR_ERR__OK == eRet)
    {
        memcpy(pParam, &tIspLdrInfo, sizeof(tIspLdrInfo));
    }

    return 0;
}

void NvrFixInitLdr()
{
    TNvrCapLcam tCapLcamInfo;
    NvrCapGetCapParam(NVR_CAP_ID_LCAM, (void*)&tCapLcamInfo);

#ifndef _QCOM_
    if (tCapLcamInfo.tLcamIspInfo.atLcamIsp[0].tDayNightSwitch.abDNModeSupport[NVR_ISP_IRCUT_MODE_LDRAUTO])
    {
        PRINTDBG("regist ldr \n");
        IspCbRegister(0, ISP_REGFUN_SET_LDRVALUE, NvrFixIspLDRCb);
    }
#endif
}


void NvrFixUserSafeInit()
{
    s8 achPtzVer[NVR_FIX_DEV_MAX_SOFTVER_LEN + 1];

    memset(achPtzVer,0,sizeof(achPtzVer));

    if(g_bIsHost != TRUE)
    {
        if(0 != OsApi_TimerNew( &g_hTimer ))
        {
            PRINTERR("NvrFixUserSafeInit g_hTimer create failed\n");
            return;
        }
        
        OsApi_TimerSet( g_hTimer, 20*1000, NvrFixDelayCb, NULL);//延时20s
    }
    else
    {
#ifndef _CV2X_
        strcpy(achPtzVer,"0.0.2");//赋予云台版本号默认值
        NvrSysSetClipVer(achPtzVer);
#endif
    }
}
void NvrFixIpcVer(u8 byOn)
{
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, TRUE);
    PRINTDBG(  "** ipccore ver: %s %s **\n",__DATE__,__TIME__);
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, FALSE);
    return;
}

void NvrFixOpenssh(u8 byOn)
{
    TNvrJniMsgData tMobileMsgData;
    TNvrJniMsgInfo tMobileCallback;
    
    mzero(tMobileMsgData);
    mzero(tMobileCallback);

    if(byOn == 1)
    {
        tMobileMsgData.nMsgID = MSG_SET_NET_ADB_START;
    }
    else
    {
        tMobileMsgData.nMsgID = MSG_SET_NET_ADB_STOP;
    }
    tMobileCallback.wMsgType = JNIMSG_TYPE_SYS;
    tMobileCallback.nSize=sizeof(TNvrJniMsgData);
    tMobileCallback.pData=&tMobileMsgData;

    NvrSysSndJniMsg(&tMobileCallback, __FUNCTION__, __LINE__);

    return;
}



s32 NvrFixGetPtzPosition(u8 byDevId,u16 *pwPangle, u16 *pwTangle,u32 *pdwZmPos,u32 *pdwFcsPos)
{
    if(byDevId < 1 || byDevId > 3)
    {
        return -1;
    }
    
    if(byDevId == 1)
    {
        IspActionParam(0, ISP_ACT_GET_ZOOM_POSITION, pdwZmPos);
        IspActionParam(0, ISP_ACT_GET_FOCUS_POSITION, pdwFcsPos);
    }
    else
    {                
        //发给从机请求
        NvrFixHostToSlaveMessage(NVR_PTZ_ONVIFTRANS_TYPE_DEV_GET,(void *)&byDevId);

        //等待从机回复
         OsApi_SemTakeByTime(g_hHSCSem,5000);

         *pwPangle = g_tOnvifDevMsg.wPangle;
         *pwTangle = g_tOnvifDevMsg.wTangle;
         *pdwZmPos = g_tOnvifDevMsg.dwZmPos;
         *pdwFcsPos = g_tOnvifDevMsg.dwFcsPos;
    }

    PRINTDBG("NvrFixGetPtzPosition devid %u,p %u,t %u,z %lu,f %lu\n",byDevId,*pwPangle,*pwTangle,*pdwZmPos,*pdwFcsPos);

    return 0;
}

s32 NvrFixSetPtzPosition(u8 byDevId,u16 dwPangle, u16 dwTangle,u32 dwZmPos)
{
    TNvrOnvifDevMsg tDev;

    if(byDevId < 1 || byDevId > 3)
    {
        return -1;
    }

    if(byDevId == 1)
    {
		NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&dwZmPos);
    }
    else
    {        
        tDev.byDevId = byDevId;
        tDev.wPangle = dwPangle;
        tDev.wTangle = dwTangle;
        tDev.dwZmPos = dwZmPos;
        
        //发给从机请求
        NvrFixHostToSlaveMessage(NVR_PTZ_ONVIFTRANS_TYPE_DEV_SET,(void *)&tDev);
        
        sleep(2);
    }

    PRINTDBG("NvrFixSetPtzPosition devid %u,p %u,t %u,z %lu\n",byDevId,dwPangle,dwTangle,dwZmPos);

    return 0;
}


void NvrFixInitProbe()
{
	TNvrGuardTaskParam tTaskParam;
	
	mzero(tTaskParam);

    strcpy(tTaskParam.achTaskName,"NvrFtpProbeCheck");
    tTaskParam.bEnable = TRUE;
    tTaskParam.dwCheckTime = 60*1;
    tTaskParam.pfGuardDealCB = NvrFtpProbeUpload;   ///探针上传检测接口
    NvrGuardTaskRegist(&tTaskParam);
    
    NvrProbeTransDataSetReportCB(NvrProbeWriteFtpFile);
    
}

void NvrFixPtzVer()
{
    OspPrintf(1,0,"NvrFixPtzVer %s\n",g_achPtzVer);
}


void NvrFixDevPtzTest(u8 byIndex, u32 dwParam1, u32 dwParam2, u32 dwParam3,u32 dwParam4)
{
    TNvrPtzCtrlInfo tPtzCtrlInfo;
	u8 bySerialId = 0;

    mzero(tPtzCtrlInfo);

	PRINTDBG("LcamPtzTest %lu-%lu-%lu\n",dwParam1,dwParam2,dwParam3);

    switch(byIndex)
    {
        case 0://断电恢复
        {
           NvrFixDevPtzExtraOperate();
        }
        break;
        case 1://手动设置ptz坐标
        {
            bySerialId = (u8)dwParam1;
            
            if(dwParam2 == 0)
            {
                tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_PANPOSION_SET;
                tPtzCtrlInfo.wXposition = (u16)dwParam3;
                NvrFixDevPtzCtrl(bySerialId,tPtzCtrlInfo);
            }
            else
            {
                tPtzCtrlInfo.eCtrlType = NVR_PTZCTRL_TILTPOSION_SET;
                tPtzCtrlInfo.wYposition = (u16)dwParam3;
                NvrFixDevPtzCtrl(bySerialId,tPtzCtrlInfo);
            }
        }
        break;
        case 2://获取ptz坐标
            {
                u16 wPangle,wTangle;
                u32 dwZmPos,dwFcsPos;
                
                NvrFixGetPtzPosition((u8)dwParam1,&wPangle,&wTangle,&dwZmPos,&dwFcsPos);
            }
            break;
        case 3://设置ptz坐标
            {
                NvrFixSetPtzPosition((u8)dwParam1,(u16)dwParam2,(u16)dwParam3,(u32)dwParam4);
            }
            break;
		case 4://设置zoom参数
			{
				NvrFixIspSetKeyParam(0,NVR_FIX_ISP_ACTION_KEY_ZOOM_POSIITION,(void *)&dwParam1);
			}
			break;
		case 5://设置focus参数
			{	
				IspActionParam(0,ISP_ACT_SET_FOCUS_POSITION,(void *)&dwParam1);
			}
			break;
        default:
		    break;
    }

    return;
}

void NvrFixGetIdr()
{
	u8 i = 0;
	u32 dwLdrValue = 0;
	THwmonStat tHwmonStat;
	
	mzero(tHwmonStat);
	
	tHwmonStat.dwId = HWMON_ID(HWMON_TYPE_BRIGHT, 0);

	//生产需求，每隔200ms查询一次总共20次
	for (i = 0; i < 20; i++)
	{
		NvrBrdApiHwmonGetStatus(&tHwmonStat);
        dwLdrValue = tHwmonStat.tEntry.tBright.dwCur ;//光敏阻值
		OspPrintf(1,0,"[IPCSRV]getldr ldr value:%u\n",dwLdrValue);
		OsApi_TaskDelay(200);
	}

	return;
}

void NvrFixGetDnThreshold()
{
    TNvrBrdPrdInfo tPrdInfo;
    NVRSTATUS eRet;

    mzero(tPrdInfo);//lint !e419

    /**获取e2prom信息*/
    eRet = NvrBrdApiBrdPinfoQuery(&tPrdInfo);
    if(eRet != NVR_ERR__OK)
    {
        OspPrintf(1,0,"BrdPinfoQuery fail,ret:%d\n",eRet);
        return;
    }

    ///< 兼容老代码：之前光敏电阻阈值写在了第49字节，现在修正为第20个字节
    if(0 == (tPrdInfo.abyUserData[19]&0x80))
	{
    	//OspPrintf(1,0,"no DN Threshold ,userdate[19]: %d\n", tPrdInfo.abyUserData[19]);

        if(0 == (tPrdInfo.abyUserData[48]&0x80))
        {
            OspPrintf(1,0,"no DN Threshold ,userdate[48]: %d\n", tPrdInfo.abyUserData[48]);
        }
        else
        {
            OspPrintf(1,0,"get default DN Threshold : %d\n", (tPrdInfo.abyUserData[48]&0x7F));
        }
	}
	else
	{
		OspPrintf(1,0,"get default DN Threshold : %d\n", (tPrdInfo.abyUserData[19]&0x7F));
	}

	return;
}

void NvrFixSwitchIrcut(u8 status)
{
    NvrBrdApiSetIRCutStatus(status);
}

NVRSTATUS NvrFixInitMultiSerial(u8 bySerialId)
{
    TSerialInfo tSerialInfo;

	mzero(tSerialInfo);
    u32 dwFixBaudrate = 115200;
    if(g_hMultiSerialHandle[bySerialId] < 0)
    {
        PRINTDBG("NvrFixInitMultiSerial serial id:%d\n", bySerialId);
    }
    else
    {
        return NVR_ERR__OK;
    }

    tSerialInfo.dwNo = bySerialId;
    NvrBrdApiSerialQueryInfo(&tSerialInfo);
    g_hMultiSerialHandle[bySerialId] = NvrBrdApiSerialOpen(&tSerialInfo);

    if(g_hMultiSerialHandle[bySerialId] < 0)
    {
        PRINTERR("IpcInitMultiSerial open serial (%d) failed\n", bySerialId);
        return NVR_ERR__ERROR;
    }
    else
    {
    //爱芯的需要设置波特率，其他的用默认波特率
#ifdef _AX603A_
    	NvrBrdApiSerialIoctl(g_hMultiSerialHandle[bySerialId], SIO_SET_BAUDRATE, &dwFixBaudrate);
#endif
        OspPrintf(1,0,"NvrFixInitMultiSerial g_hMultiSerialHandle[%d]:%d\n", bySerialId, g_hMultiSerialHandle[bySerialId]);
    }
    MAKE_COMPILER_HAPPY(dwFixBaudrate);
	return NVR_ERR__OK;
}

NVRSTATUS NvrFixWriteMultiSerial(u8 *pbyWrBuf, u16 wWrLen, u16 *pwRealLen, u8 bySerialId)
{
	u32 i = 0;
	NVRSTATUS eRet = NVR_ERR__ERROR;

    if(g_hMultiSerialHandle[bySerialId] < 0)
	{
		if(NVR_ERR__OK != NvrFixInitMultiSerial(bySerialId))
		{
			return eRet;
		}
	}

	*pwRealLen = NvrBrdApiSerialWrite(g_hMultiSerialHandle[bySerialId], pbyWrBuf, wWrLen);
	if(*pwRealLen == wWrLen)
	{
		OspPrintf(1,0,"NvrFixWriteMultiSerial SerialId:%d: Wrlen:%d Data:",bySerialId, wWrLen);
		for( i = 0; i < wWrLen; i++ )
		{
			OspPrintf(1,0," %.2x",pbyWrBuf[i]);
		}
		OspPrintf(1,0,"\n");
		
		eRet = NVR_ERR__OK;
	}
	else
	{
		OspPrintf(1,0,"NvrFixWriteMultiSerial error Wrlen:%d, RealLen:%d !\n", wWrLen, *pwRealLen);
		eRet = NVR_ERR__ERROR;
	}
    
    return eRet;
}

NVRSTATUS NvrFixReadMultiSerial(u8 *pbyReadBuf, u16 wReadLen, u16 *pwRealLen, u8 bySerialId, u32 dwTimeOut)
{
	fd_set rcvset;
	struct timeval tTimeVal;
	NVRSTATUS eRet = NVR_ERR__ERROR;
	s32 nRet = -1;
	u32 i = 0;

    OspPrintf(1,0,"NvrFixReadMultiSerial read bySerialId:%d\n", bySerialId);
    
	if(g_hMultiSerialHandle[bySerialId] < 0)
	{
		if(NVR_ERR__OK != NvrFixInitMultiSerial(bySerialId))
		{
			return eRet;
		}
	}

	tTimeVal.tv_sec	 = 0;
	if(dwTimeOut == 0 )
	{
		dwTimeOut = 200;		///<读串口超时时间默认200ms
	}
	tTimeVal.tv_usec = dwTimeOut*1000;

	FD_ZERO(&rcvset);

	FD_SET(g_hMultiSerialHandle[bySerialId], &rcvset);

	nRet = select(g_hMultiSerialHandle[bySerialId] + 1, &rcvset, NULL, NULL, &tTimeVal);
	if(nRet > 0)
	{
		if(!FD_ISSET(g_hMultiSerialHandle[bySerialId], &rcvset))
		{
			OspPrintf(1,0,"NvrFixReadMultiSerial FD_ISSET(%d) failed.\n", bySerialId) ;
			return eRet;
		}

		nRet = NvrBrdApiSerialRead(g_hMultiSerialHandle[bySerialId], pbyReadBuf, wReadLen);

		if(nRet < 0)
		{
			OspPrintf(1,0,"NvrFixReadMultiSerial read failed %d\n", nRet);
			return eRet;
		}
		else
		{
			*pwRealLen = nRet;
			OspPrintf(1,0,"NvrFixReadMultiSerial len: %d Data: ", *pwRealLen);
			for(i = 0; i < *pwRealLen; i++)
			{
				OspPrintf(1,0,"0x%x ", pbyReadBuf[i]);
			}
			OspPrintf(1,0,"\n");

			eRet = NVR_ERR__OK;
		}

	}
	else
	{
		eRet = NVR_ERR__ERROR;
		OspPrintf(1,0,"NvrFixReadMultiSerial select wait time out %d\n", nRet);
	}

	return eRet;
}

void *NvrFixSerialEchoThread()
{
	u16 wRealReadLen = 0;
	u16 wRealWriteLen = 0;
	u8 byReadBuf[256] = {0};
	u8 bySerialId[NVR_FIX_MAX_MULTISERIAL_NUM] = {0};
	bySerialId[0] = 0;
	bySerialId[1] = 1;
	bySerialId[2] = 3;
	bySerialId[3] = 2;

    TThreadInfoRecord tThreadInfo;
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "NvrFixSerialEcho");
    OsApi_AddThreadInfo( &tThreadInfo );
	
#ifndef WIN32
    prctl(PR_SET_NAME, "NvrFixSerialEcho", 0, 0, 0);
#endif
	while(1)
	{
	    if(g_bySerialTestFlag == 0)
	    {
	    	sleep(1);
	    }
		else
		{	wRealReadLen = 0;
			NvrFixReadMultiSerial(byReadBuf, sizeof(byReadBuf), &wRealReadLen, bySerialId[0], 0);
			if(wRealReadLen != 0)
			{
				NvrFixWriteMultiSerial(byReadBuf, wRealReadLen, &wRealWriteLen, bySerialId[0]);
			}
			wRealReadLen = 0;
			NvrFixReadMultiSerial(byReadBuf, sizeof(byReadBuf), &wRealReadLen, bySerialId[1],0);
			if(wRealReadLen != 0)
			{
				NvrFixWriteMultiSerial(byReadBuf, wRealReadLen, &wRealWriteLen, bySerialId[1]);
			}
			wRealReadLen = 0;
			NvrFixReadMultiSerial(byReadBuf, sizeof(byReadBuf), &wRealReadLen, bySerialId[2], 0);
			if(wRealReadLen != 0)
			{
				NvrFixWriteMultiSerial(byReadBuf, wRealReadLen, &wRealWriteLen, bySerialId[2]);
			}

			wRealReadLen = 0;
			NvrFixReadMultiSerial(byReadBuf, sizeof(byReadBuf), &wRealReadLen, bySerialId[3], 0);
			if(wRealReadLen != 0)
			{
				NvrFixWriteMultiSerial(byReadBuf, wRealReadLen, &wRealWriteLen, bySerialId[3]);
			}

		}
	}

}

void NvrFixXs5012NeedUpgrade()
{

#ifdef _SSC339G_
    TNvrCapLcam tCapLcamInfo;
    NvrCapGetCapParam(NVR_CAP_ID_LCAM, (void *)&tCapLcamInfo);
    if (NVR_CAP_LCAMMC_PARAM_SPECIAL_339G2241 == tCapLcamInfo.tLcamMcInfo.eParamSpecial)
    {

        u32 firstVer;
        u32 secondVer;
        u32 fw_vernum = 0;
        u32 firstDftVer = 0;   ///<默认主版本号
        u32 sencondDftVer = 0; ///<默认次版本
        char achVer[4];
        const s8 *ps8AppFilePath = "/usr/bin/xs5012_app.bin";
        FILE *pfile;
        g_bSlaveNeedUpdate = FALSE;
        if (access(ps8AppFilePath, F_OK) == 0)
        {
            ///<读取版本号
            pfile = fopen(ps8AppFilePath, "rb");
            if (!pfile)
            {
                OspPrintf(1, 0, "File %s  Open Faild!\n", ps8AppFilePath);
                return;
            }
            if (!fread(achVer, 1, 4, pfile))
            {
                fclose(pfile);
                OspPrintf(1, 0, "File %s  Read Faild!\n", ps8AppFilePath);
                return;
            }
            fclose(pfile);
            firstDftVer = achVer[1];
            sencondDftVer = achVer[3];
        }
        u32 ret = SysGetFwVerNum(3, &fw_vernum);
        if (ret < 0)
        {
            g_bSlaveNeedUpdate = FALSE;
            OspPrintf(1, 0, "read error!\n");
            return;
        }

        firstVer = (fw_vernum >> 16) & 0xff;
        secondVer = (fw_vernum)&0xff;
        if (firstVer > firstDftVer)
        {
            g_bSlaveNeedUpdate = FALSE;
            return;
        }
        if ((firstVer == firstDftVer) && secondVer >= sencondDftVer)
        {
            g_bSlaveNeedUpdate = FALSE;
            return;
        }
        PRINTDBG("Need Upgrade\n");
        g_bSlaveNeedUpdate = TRUE;
		return;
    }
#endif
}

///设置从机升级
static void NvrFixSetXs5012Update()
{
	
	char achCmd[128];
	const s8 *ps8UpdateIspFilePath = "/usr/ispupdate.flag";
	
	///<创建isp升级标志文件
	snprintf(achCmd, sizeof(achCmd), "touch %s", ps8UpdateIspFilePath);
	NvrSystem(achCmd);

	///<重启
	NvrSystem("echo 0 > /usr/config/reboot_cnt");
	NvrSysReboot(NULL);

}
static void NvrFixSetXs5012UpdateCb()
{
	//正在升级中
	if(g_bSlaveUpdating)
	{
		return;
	}
	
	g_bSlaveUpdating = TRUE;
	if(0 != OsApi_TimerNew( &g_hSlaveUpgradeTimer ))
    {
           PRINTERR("g_hSlaveUpgradeTimer create failed\n");
           return;
    }    
    OsApi_TimerSet( g_hSlaveUpgradeTimer, 2*1000, NvrFixSetXs5012Update, NULL);//延时2s
}

///从机升级状态
static void NvrFixSlaveUpgradeStateCb(TNvrSlaveUpgradeState *ptSlaveUpgradeState)
{
	if(g_bSlaveUpdating)
	{
		ptSlaveUpgradeState->eState =  NVR_DEV_UPGRADE_STATE_SENDING;
	}
	else
	{
		ptSlaveUpgradeState->eState =  NVR_DEV_UPGRADE_STATE_DONE;
	}
	
}
static void NvrFixSlaveNeedUpgradeCb(BOOL32 *pbNeedUpgrade)
{
#ifdef _SSC339G_	
	*pbNeedUpgrade = g_bSlaveNeedUpdate;
	 OspPrintf(1,0,"slave need upgrade %d !\n", g_bSlaveNeedUpdate);

#endif
	return;

}


///<获取从机升级标志
static void NvrFixGetXs5012UpdateFlag()
{
	///<存在升级标志文件即在升级中
	const s8 *ps8UpdateFilePath = "/usr/update.flag";
	const s8 *ps8UpdateIspFilePath = "/usr/ispupdate.flag";
	if(access(ps8UpdateFilePath, F_OK) == 0 || access(ps8UpdateIspFilePath, F_OK) == 0)
	{
		g_bSlaveUpdating = TRUE;
	}
	else
	{
		g_bSlaveUpdating = FALSE;
	}

}

///<2241从机升级
static void NvrFixXs5012Update()
{
#ifdef _SSC339G_
	u32 ret;
	u32 u32Res = 0;
	char achCmd[128];
	const s8 *ps8AppFilePath = "/usr/bin/xs5012_app.bin";
	const s8 *ps8IspFilePath = "/usr/bin/aisp_cfg.bin";
	const s8 *ps8UpdateFilePath = "/usr/update.flag";
	const s8 *ps8UpdateIspFilePath = "/usr/ispupdate.flag";
	TNvrCapLcam tCapLcamInfo;
    NvrCapGetCapParam(NVR_CAP_ID_LCAM, (void*)&tCapLcamInfo);
	if(NVR_CAP_LCAMMC_PARAM_SPECIAL_339G2241  == tCapLcamInfo.tLcamMcInfo.eParamSpecial)
	{
		OspPrintf(1,0,"SysFirmwareUpdate Start!\n");
		///<判断升级标记文件是否存在，再升级app文件
	    if(access(ps8UpdateFilePath, F_OK) == 0)
	    {
			if(access( ps8AppFilePath, F_OK) == 0)
			{
				OspPrintf(1,0,"update %s !\n", ps8AppFilePath);
				
				u32Res = SysFirmwareUpdate(ps8AppFilePath, 3);
				OspPrintf(1,0,"NvrFixXs5012Update NvrSysReboot !\n");
				
				snprintf(achCmd, sizeof(achCmd), "rm -f %s", ps8UpdateFilePath);
			    NvrSystem(achCmd);
				NvrSystem("echo 0 > /usr/config/reboot_cnt");
				NvrSysReboot(NULL);
				return;
			}
			else
			{
				snprintf(achCmd, sizeof(achCmd), "rm -f %s", ps8UpdateFilePath);
			    NvrSystem(achCmd);
			}
	    }
	    	
		///<先升级isp文件,创建升级标志文件
		if(access(ps8UpdateIspFilePath, F_OK) == 0)
		{
			if(access(ps8IspFilePath, F_OK) == 0)
			{
				OspPrintf(1,0,"update %s !\n", ps8IspFilePath);
				u32Res =  SysFirmwareUpdate(ps8IspFilePath, 4);
				///<创建app升级标志文件
				snprintf(achCmd, sizeof(achCmd), "touch %s", ps8UpdateFilePath);
				NvrSystem(achCmd);
				///<删除isp升级标志文件
				snprintf(achCmd, sizeof(achCmd), "rm -f %s", ps8UpdateIspFilePath);
				NvrSystem(achCmd);
				
				NvrSystem("echo 0 > /usr/config/reboot_cnt");
			    
				sleep(2);
				NvrSysReboot(NULL);
				return;
			}
			else
			{
				snprintf(achCmd, sizeof(achCmd), "rm -f %s", ps8UpdateIspFilePath);
			   	NvrSystem(achCmd);
			}
		}
			  
	}
    MAKE_COMPILER_HAPPY(u32Res);
    MAKE_COMPILER_HAPPY(ret);
#endif
	return;
}

static void NvrFixXs5012UpdateTask()
{
	///<创建线程
	if((TASKHANDLE)NULL == g_h5012UpdateTask)
	{
	     g_h5012UpdateTask = OsApi_TaskCreate((LINUXFUNC)NvrFixXs5012Update, "NvrFixXs5012Update", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL );
	     if ((TASKHANDLE)NULL == g_h5012UpdateTask)
	     {
	         OspPrintf(1,0,"NvrFixXs5012UpdateTask: Create NvrFixXs5012Update Failed\n" );
	         return ;
	     }
	 }
}


static void NvrFixXs5012Version()
{
#ifdef _SSC339G_
	u32 firstVer;
	u32 secondVer;
	u32 fw_vernum = 0;
	u32 firstDftVer = 0;	///<默认主版本号
	u32 sencondDftVer = 0;	///<默认次版本
	char achVer[4];
	const s8 *ps8AppFilePath = "/usr/bin/xs5012_app.bin";
	FILE* pfile;

	if(access(ps8AppFilePath, F_OK) == 0)
	{
		///<读取版本号
		pfile  = fopen(ps8AppFilePath,"rb");
		if(!pfile)
		{
			OspPrintf(1,0, "File %s  Open Faild!\n", ps8AppFilePath);
			return;
		}
		if(!fread(achVer, 1, 4, pfile))
		{
			fclose(pfile);
			OspPrintf(1,0, "File %s  Read Faild!\n", ps8AppFilePath);
			return;
		}
		fclose(pfile);	
		firstDftVer = achVer[1];
		sencondDftVer = achVer[3];
	}
	u32 ret = SysGetFwVerNum(3, &fw_vernum);
	if (ret < 0)
	{
		OspPrintf(1,0, "read error!\n");	
		return;
	}
	OspPrintf(1,0,"get fw version number:%u\n", fw_vernum);
	firstVer = (fw_vernum>>16)&0xff ;
	secondVer = (fw_vernum)&0xff;
	OspPrintf(1,0,"file ver: %d.%d fw version :%d.%d\n", firstDftVer, sencondDftVer, firstVer, secondVer);

#endif	
	return;
}


///<串口回显测试 0 关闭 1~4对应串口号
static void NvrFixSerialEchoTest(u8 byTestFlag)
{
#ifdef _AX603A_
	OspPrintf(1,0,"NvrFixSerialEchoTest  %d!\n", byTestFlag);
	u8 i = 0;
	u16 wSerialNum = 4;
	u8 bySerialId[NVR_FIX_MAX_MULTISERIAL_NUM] = {0};
	bySerialId[0] = 0;
	bySerialId[1] = 1;
	bySerialId[2] = 3;
	bySerialId[3] = 2;

	if(byTestFlag < 0 || byTestFlag > 1)
	{
		OspPrintf(1,0,"invalid flag  = %d !\n",byTestFlag);
		return;
	}
	for(i = 0; i < wSerialNum; i++)
	{
		if(NVR_ERR__OK != NvrFixInitMultiSerial(bySerialId[i]))
		{
			OspPrintf(1,0,"NvrFixMultiSerialTest IpcInitMultiSerial:%d failed!\n", bySerialId[i]);
			return;
		}
	}
	///<创建读取回显线程
	if((TASKHANDLE)NULL == g_hSerialEchoTask)
	{
	     g_hSerialEchoTask = OsApi_TaskCreate((LINUXFUNC)NvrFixSerialEchoThread, "NvrFixSerialEcho", NVR_TASK_COMMON_PRIORITY, 2048<<10, 0, 0, NULL );
	     if ((TASKHANDLE)NULL == g_hSerialEchoTask)
	     {
	         OspPrintf(1,0,"NvrFixSerialEchoTest: Create NvrFixSerialEchoThread Failed\n" );
	         return ;
	     }
	 }
	 g_bySerialTestFlag = byTestFlag;
	
#endif

	 MAKE_COMPILER_HAPPY(g_hSerialEchoTask);
}

void NvrFixMultiSerialTest()
{
    u16 wRealLen = 0;
    u8 i = 0;
	u16 wSerialNum = 3;
    u8 byWriteBuf[8] = {0x4a, 0x45, 0x44, 0x39, 0x43, 0x4F, 0x4f, 0x00};
    u8 byReadBuf[3][8] = {{0},{0},{0}};
	u8 bySerialId[NVR_FIX_MAX_MULTISERIAL_NUM] = {0};
#ifdef _AX603A_
	bySerialId[0] = 0;
	bySerialId[1] = 1;
	bySerialId[2] = 3;
	wSerialNum = 3;
#else
    bySerialId[0] = 1;
	bySerialId[1] = 2;
	bySerialId[2] = 3;
	wSerialNum = 3;
#endif
    for(i = 0; i < wSerialNum; i++)
    {
        if(NVR_ERR__OK != NvrFixInitMultiSerial(bySerialId[i]))
        {
            OspPrintf(1,0,"NvrFixMultiSerialTest IpcInitMultiSerial:%d failed!\n", bySerialId[i]);
            return;
        }
    }

    ///<创建信号量
    if(g_hMultiSerialSem == NULL)
    {
        if(!OsApi_SemBCreate(&g_hMultiSerialSem))
        {
            PRINTERR("create g_hMultiSerialSem failed\n");
            return;
        }
    }

    OsApi_SemTake(g_hMultiSerialSem);
    NvrFixWriteMultiSerial(byWriteBuf, sizeof(byWriteBuf), &wRealLen, bySerialId[0]);
    NvrFixReadMultiSerial(byReadBuf[1], sizeof(byReadBuf[1]), &wRealLen, bySerialId[1], 200);
    OsApi_SemGive(g_hMultiSerialSem);

    OsApi_SemTake(g_hMultiSerialSem);
    if(wRealLen == sizeof(byReadBuf[1]) && 0x4a == byReadBuf[1][0] && 0x45 == byReadBuf[1][1] && 0x44 == byReadBuf[1][2]
        && 0x39 == byReadBuf[1][3] && 0x43 == byReadBuf[1][4] && 0x4F == byReadBuf[1][5] && 0x4f == byReadBuf[1][6] && 0x00 == byReadBuf[1][7])
    {
        NvrFixWriteMultiSerial(byReadBuf[1], sizeof(byReadBuf[1]), &wRealLen, bySerialId[1]);
    }
    else
    {
        OspPrintf(1,0,"NvrFixMultiSerialTest 1 -> 2 failed!\n");
        OsApi_SemGive(g_hMultiSerialSem);
        return;
    }
    NvrFixReadMultiSerial(byReadBuf[0], sizeof(byReadBuf[0]), &wRealLen, bySerialId[2], 200);
    OsApi_SemGive(g_hMultiSerialSem);

    OsApi_SemTake(g_hMultiSerialSem);
    if(wRealLen == sizeof(byReadBuf[0]) && 0x4a == byReadBuf[0][0] && 0x45 == byReadBuf[0][1] && 0x44 == byReadBuf[0][2]
        && 0x39 == byReadBuf[0][3] && 0x43 == byReadBuf[0][4] && 0x4F == byReadBuf[0][5] && 0x4f == byReadBuf[0][6] && 0x00 == byReadBuf[0][7])
    {
        NvrFixWriteMultiSerial(byReadBuf[1], sizeof(byReadBuf[1]), &wRealLen, bySerialId[2]);
    }
    else
    {
        OspPrintf(1,0,"ipcMultiSerialTest 2 -> 3 failed!\n");
        OsApi_SemGive(g_hMultiSerialSem);
        return;
    }
    NvrFixReadMultiSerial(byReadBuf[2], sizeof(byReadBuf[2]), &wRealLen, bySerialId[0], 200);
    OsApi_SemGive(g_hMultiSerialSem);


    if(wRealLen == sizeof(byReadBuf[2]) && 0x4a == byReadBuf[2][0] && 0x45 == byReadBuf[2][1] && 0x44 == byReadBuf[2][2]
        && 0x39 == byReadBuf[2][3] && 0x43 == byReadBuf[2][4] && 0x4F == byReadBuf[2][5] && 0x4f == byReadBuf[2][6] && 0x00 == byReadBuf[2][7])
    {
        OspPrintf(1,0,"NvrFixMultiSerialTest OK!\n");
    }
    else
    {
        OspPrintf(1,0,"NvrFixMultiSerialTest 3 -> 1 failed!\n");
    }

    return;

}

void NvrFixClearE2promInfo()
{
    OspPrintf(1,0,"NvrFixClearE2promInfo Manual Clear e2prominfo !\n");

    s32 nRet =  BrdPinfoClean();
    if (0 != nRet)
    {
        OspPrintf(1,0,"NvrFixClearE2promInfo error ret:%d\n", nRet);
        return;
    }

    //同时删除E2PROM备份
    remove(""NVR_FIX_E2PBACKUP_PATH"");

    OspPrintf(1,0,"NvrFixClearE2promInfo erase eeprom info success!\n");

    return;
}

void NvrFixSetTestFlag(u8 byFlag)
{
    s32 nRet = 0;

    OspPrintf(1,0,"NvrFixSetTestFlag Manual Set Test Flag:%u !\n",byFlag);

    if (0 == byFlag)        //清除生产测试标志
    {
        nRet = BrdPinfoFlag0Ops(FL0_TST_CLEAR,0);
        if (0 != nRet)
        {
            OspPrintf(1,0,"NvrFixSetTestFlag clear test flag error ret:%d\n", nRet);
            return;
        }
        else
        {
            OspPrintf(1,0,"NvrFixSetTestFlag clear test flag success ret:%d\n", nRet);

        }
    }
    else if (1 == byFlag)   //设置标志位
    {
        nRet = BrdPinfoFlag0Ops(FL0_TST_SET,0);
        if (0 != nRet)
        {
            OspPrintf(1,0,"NvrFixSetTestFlag set test flag error ret:%d\n", nRet);
            return;
        }
        else
        {
            OspPrintf(1,0,"NvrFixSetTestFlag set test flag success ret:%d\n", nRet);
        }

    }
    else
    {
        OspPrintf(1,0,"NvrFixSetTestFlag input param invalid %d !!!\n", byFlag);
    }

    return;
}

void *NvrFixShowIspInfoThread(u32 byChnId)
{
    TThreadInfoRecord tThreadInfo;
    u32 dwLdrValue = 0;
    u32 dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
	s8 achTemp[NVR_MAX_OSD_CONTENT_LEN] = {0};
	u8 abyStr[NVR_MAX_OSD_CONTENT_LEN] = {0};
	TNvrOsdMobileUpdateContent tUpdateContent;
    TNvrCapLcam tCapLcam;
    u8 byIspChn = 0;

    mzero(tUpdateContent);
    mzero(tCapLcam);

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);

	tUpdateContent.byChnId = 0;
	tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_USER;
    //添加线程信息
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "NvrFixShowIspInfo");
    OsApi_AddThreadInfo( &tThreadInfo );

#ifndef WIN32
    prctl(PR_SET_NAME, "NvrFixShowIspInfo", 0, 0, 0);
#endif

	//设置线程为分离状态，防止内存泄露
	pthread_detach(pthread_self());

	OspPrintf(1,0,"NvrFixShowIspInfoThread chnid:%d  Task start...\n",byChnId);

    if(NVR_CAP_LCAM_CAPTURE_ENCSONESOURCE == tCapLcam.tLcamMcInfo.tCapture.eCapType)   ///<多通道单源
    {
        if(NVR_CAP_VID_CHN_VIN_BT1120_1 == tCapLcam.tLcamMcInfo.atVidCap[byChnId].dwMediaCtrlCapChn)
        {
            return NULL;
        }
        byIspChn = (s32)(tCapLcam.tLcamMcInfo.atVidCap[byChnId].dwMediaCtrlCapChn - MEDIACTRL_VID_CHN_VIN_SENSOR_0);
    }

    while (s_bShowIspInfo[byChnId])
    {

        tUpdateContent.byChnId = byChnId;
		///<shutter
		memset(abyStr, 0, sizeof(abyStr));
		mzero(achTemp); 
		mzero(tUpdateContent.abyContent);
		tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_USER;
		tUpdateContent.wOsdId = 0;
        IspDoCmd(byIspChn, ISP_CMD_GET_SHUTTER_TIME, &dwLdrValue);
		PRINTDBG("byChnId:%d byIspId:%d Shutter:1/%d\n",byChnId,byIspChn,dwLdrValue);
		sprintf(achTemp, "Shutter: 1/%d", (s32)dwLdrValue);
		dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		CharConvConvertGbktoUnicode(achTemp, abyStr, &dwStrLen);
		memcpy(tUpdateContent.abyContent,abyStr,dwStrLen);
		tUpdateContent.byBufLen = dwStrLen;
		NvrOsdMobileUpdateOsd(&tUpdateContent);

		///<gain
		memset(abyStr, 0, sizeof(abyStr));
		mzero(achTemp);
		mzero(tUpdateContent.abyContent);
		tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_USER;
		tUpdateContent.wOsdId = 1;
        IspDoCmd(byIspChn, ISP_CMD_GET_SYSTEM_GAIN, &dwLdrValue);
        sprintf(achTemp, "Gain: %d", (s32)dwLdrValue);
		dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		PRINTDBG("byChnId:%d byIspId:%d Gain:%d\n",byChnId,byIspChn,dwLdrValue);
		CharConvConvertGbktoUnicode(achTemp, abyStr, &dwStrLen);
		memcpy(tUpdateContent.abyContent,abyStr,dwStrLen);
		tUpdateContent.byBufLen = dwStrLen;
		NvrOsdMobileUpdateOsd(&tUpdateContent);
	

		///<Iris
		memset(abyStr, 0, sizeof(abyStr));
		mzero(achTemp); 
		mzero(tUpdateContent.abyContent);
		tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_USER;
		tUpdateContent.wOsdId = 2;
		IspActionParam(byIspChn, ISP_ACT_GET_IRIS_POSITION,&dwLdrValue);
		PRINTDBG("byChnId:%d byIspId:%d Iris:%d\n",byChnId,byIspChn,dwLdrValue);
        sprintf(achTemp, "Iris: %d", (s32)dwLdrValue);
		dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		CharConvConvertGbktoUnicode(achTemp, abyStr, &dwStrLen);
		memcpy(tUpdateContent.abyContent,abyStr,dwStrLen);
		tUpdateContent.byBufLen = dwStrLen;
		NvrOsdMobileUpdateOsd(&tUpdateContent);
		///<ldr
		memset(abyStr, 0, sizeof(abyStr));
		mzero(achTemp); 
		mzero(tUpdateContent.abyContent);
		tUpdateContent.eOsdType = NVR_OSD_ALLTYPE_USER;
		tUpdateContent.wOsdId = 3;
		NvrFixIspGetLDR(&dwLdrValue);
		PRINTDBG("byChnId:%d Ldr:%d\n",byChnId,dwLdrValue);
        snprintf(achTemp, sizeof(achTemp),"Ldr: "FORMAT_U32"", dwLdrValue);
		dwStrLen = NVR_MAX_OSD_CONTENT_LEN;
		CharConvConvertGbktoUnicode(achTemp, abyStr, &dwStrLen);
		memcpy(tUpdateContent.abyContent,abyStr,dwStrLen);
        tUpdateContent.byBufLen = dwStrLen;
		NvrOsdMobileUpdateOsd(&tUpdateContent);
        sleep(2);
    }

    OsApi_DelThreadInfo( tThreadInfo.m_dwThreadId );

    g_hShowIspTask[byChnId] = (TASKHANDLE)NULL;

    OspPrintf(1,0,"NvrFixShowIspInfoThread chnid:%d  Task Quit...\n",byChnId);
    OsApi_TaskExit();

    return NULL;
}

void NvrFixShowIspInfo(u8 byEnable, u8 byChn)
{
	if(byChn > NVR_MAX_LCAM_CHN_NUM)
	{
		OspPrintf(1,0,"NvrFixShowIspInfo: byChnId > NVR_MAX_LCAM_CHN_NUM \n",byChn );
		return;
	}
	TNvrOsdParam tOsdParam;
    mzero(tOsdParam);
    NvrOsdGetParam(byChn, &tOsdParam);
	static BOOL32 bFlag = FALSE; 
	static TNvrOsdParam tOsdOldParam = {0};
	NVRSTATUS eRet = NVR_ERR__OK;
    TNvrCapDefLcamCfg tCapDefaultCfg;
    s32 nDefaultSize = 64;

    mzero(tCapDefaultCfg);

    eRet = NvrCapGetDefCfgParam(NVR_CAP_DEF_CFG_ID_LCAM, &tCapDefaultCfg);
    if (NVR_ERR__OK != eRet)
    {
        PRINTERR("get cap def cfg failed ret:%d\n", eRet);
    }
    else
    {
        nDefaultSize = tCapDefaultCfg.tLcamOsd.atLcamOsd[byChn][0].abyFontSize[NVR_OSD_ENC_RES_1080P];
    }

    s_bShowIspInfo[byChn] = byEnable;

    PRINTERR("show chn :%d\n", byChn);
    if (byEnable)
    {
        if(FALSE == bFlag)
        {
            mzero(tOsdOldParam);
            memcpy(&tOsdOldParam,&tOsdParam,sizeof(TNvrOsdParam));
            bFlag = TRUE;
        }

        OspPrintf(1,0,"NvrFixShowIspInfo show start\n");
        tOsdParam.atNvrUserParam[3].wPosX =64;
        tOsdParam.atNvrUserParam[3].wPosY = 760;
        tOsdParam.atNvrUserParam[3].byShow = 1;
        tOsdParam.atNvrUserParam[3].eContextType = NVR_OSD_CONTEXT_TYPE_DEV;
        tOsdParam.atNvrUserParam[3].byDevLineId = 3;
        tOsdParam.atNvrUserParam[3].wWidth = nDefaultSize*20;
        tOsdParam.atNvrUserParam[3].wHeight = nDefaultSize;

        tOsdParam.atNvrUserParam[2].wPosX =64;
        tOsdParam.atNvrUserParam[2].wPosY = 824;
        tOsdParam.atNvrUserParam[2].byShow = 1;
        tOsdParam.atNvrUserParam[2].eContextType = NVR_OSD_CONTEXT_TYPE_DEV;
        tOsdParam.atNvrUserParam[2].byDevLineId = 2;
        tOsdParam.atNvrUserParam[2].wWidth = nDefaultSize*20;
        tOsdParam.atNvrUserParam[2].wHeight = nDefaultSize;

        tOsdParam.atNvrUserParam[1].wPosX = 64;
        tOsdParam.atNvrUserParam[1].wPosY = 888;
        tOsdParam.atNvrUserParam[1].byShow = 1;
        tOsdParam.atNvrUserParam[1].eContextType = NVR_OSD_CONTEXT_TYPE_DEV;
        tOsdParam.atNvrUserParam[1].byDevLineId = 1;
        tOsdParam.atNvrUserParam[1].wWidth = nDefaultSize*20;
        tOsdParam.atNvrUserParam[1].wHeight = nDefaultSize;

        tOsdParam.atNvrUserParam[0].wPosX = 64;
        tOsdParam.atNvrUserParam[0].wPosY = 952;
        tOsdParam.atNvrUserParam[0].byShow = 1;
        tOsdParam.atNvrUserParam[0].eContextType = NVR_OSD_CONTEXT_TYPE_DEV;
        tOsdParam.atNvrUserParam[0].byDevLineId = 0;
        tOsdParam.atNvrUserParam[0].wWidth = nDefaultSize*20;
        tOsdParam.atNvrUserParam[0].wHeight = nDefaultSize;
        NvrOsdSetParam(byChn, &tOsdParam);
		if((TASKHANDLE)NULL == g_hShowIspTask[byChn])
	    {
	        g_hShowIspTask[byChn] = OsApi_TaskCreate((LINUXFUNC)NvrFixShowIspInfoThread, "NvrFixShowIspInfo", NVR_TASK_COMMON_PRIORITY, 2048<<10, (UMP_PTR)FIX_POINT_SIZE(byChn), 0, NULL );
	        if ((TASKHANDLE)NULL == g_hShowIspTask[byChn])
	        {
	            OspPrintf(1,0,"NvrFixShowIspInfo: Create IpcCountReadSerialThread chnid:%d Failed\n",byChn );
	            return ;
	        }

	    }
    }
    else
    {
    	if(TRUE == bFlag)
    	{
			NvrOsdSetParam(byChn, &tOsdOldParam);
			bFlag = FALSE;
		}
        sleep(1);
    }
    return;
}

void NvrFixSetDnThreshold(u8 byValue)
{
    int nRet = 0;
    TNvrBrdPrdInfo tPrdInfo;
    NVRSTATUS eRet;

    mzero(tPrdInfo);//lint !e419

    //获取e2prom信息
    eRet = NvrBrdApiBrdPinfoQuery(&tPrdInfo);
    if (eRet != NVR_ERR__OK)
    {
        OspPrintf(1,0,"BrdPinfoQuery fail,ret:%d\n",eRet);
        return ;
    }

	tPrdInfo.abyUserData[19] = byValue | 0x80;

	nRet = BrdPinfoSetUserdata(tPrdInfo.abyUserData, 64);
	if (nRet != 0)
    {
        OspPrintf(1,0,"BrdPinfoSetUserdata fail,ret:%d\n",nRet);
        return ;
    }

    OspPrintf(1,0,"set default DN Threshold ok!\n");
	
    if(g_bIsHost == TRUE)
	{
		NvrFixHostToSlaveMessage(NVR_PTZ_ONVIFTRANS_TYPE_LDR_THR, &byValue);
	}

	return;
}

s32 NvrFixSmtpSend(u16 wChnId,char *pchPicData,u32 dwPicDataLen)
{
    FILE *pfile = NULL;
    char achFileName[NVR_MAX_STR256_LEN] = {""};

    ///保证文件完整性

	///mpu 通道号从0开始，alarm模块0代表NVR
	sprintf(achFileName,"/tmp/snap_D%d.jpg",wChnId);
    pfile  = fopen(achFileName,"w");
    if(NULL != pfile)
    {
        fwrite(pchPicData,dwPicDataLen, 1, pfile);
        fflush(pfile);
        fclose(pfile);
    }
	else
	{
		PRINTERR("NvrFixSmtpSend pfile is NULL \n");
	}

    return 0;
}

void NvrFixSnapPicSnapCB(s32 dwCapChn, TMediaCtrlSnapCallbackInfo *ptSnapCBInfo)
{
	NVRSTATUS eRet= NVR_ERR__OK;
	TNvrRecSnapPicInfo tSnapPicInfo;
	struct timeval tCurTime;
	TNvrPuiVidEncParam tVidEncParam;
	
	mzero(tSnapPicInfo);
	
	PRINTDBG("NvrFixSnapPicSnapCB dwCapChn:%lu snap cb,dwContext:%d\n",dwCapChn,ptSnapCBInfo->dwContext);

	if(MEDIACTRL_SNAP_STATUS_OK != ptSnapCBInfo->eOrgStatus)
	{
		PRINTERR("NvrFixSnapPicSnapCB manual snap OrgStatus :%d not ok\n",ptSnapCBInfo->eOrgStatus);
		OsApi_SemGive(g_hFixSnapSem);
		return;
	}

	///<获取分辨率
	eRet = NvrPuiGetDevParam(0,0,NVR_PUI_VID_ENC_PARAM,&tVidEncParam);
	if(NVR_ERR__OK != eRet)
	{
		return;
	}

	gettimeofday(&tCurTime, NULL);
	tSnapPicInfo.eSnpEvt = g_eFixSnpEvt;
	tSnapPicInfo.wChnId = 0;
	tSnapPicInfo.wHeight = tVidEncParam.wVidHeight;
	tSnapPicInfo.wWidth = tVidEncParam.wVidWide;
	tSnapPicInfo.dwTaskId = 0;
	tSnapPicInfo.qwTime = tCurTime.tv_sec*1000+tCurTime.tv_usec/1000;//image 生成时间,单位ms
	tSnapPicInfo.dwPicDataLen = ptSnapCBInfo->tOrgPicInfo.dwPicLen;
	tSnapPicInfo.pPicData = (s8 *)ptSnapCBInfo->tOrgPicInfo.pPicAddr;
	snprintf((s8*)tSnapPicInfo.achPicName, sizeof(tSnapPicInfo.achPicName), "%010lu.jpeg", tCurTime.tv_sec);//image名
	
	eRet = NvrRecSaveSnapPic(&tSnapPicInfo);
	if(NVR_ERR__OK == eRet)
	{
		PRINTDBG("NvrRecSaveSnapPic sucess eRet:%d\n", eRet);
	}
	else
	{
		PRINTERR("NvrRecSaveSnapPic failed eRet:%d\n",eRet);
	}
	
	OsApi_SemGive(g_hFixSnapSem);

}

void NvrFixMultiSrcSnapCB(s32 nCapChn, TMediaCtrlSnapCallbackInfo *ptSnapCBInfo)
{
	u32 wChnId = FIX_POINT_TO_VAL32(ptSnapCBInfo->dwContext);
	TNvrBrokenDownTime tUtcTime;
	mzero(tUtcTime);

	//防止媒控对数值进行修改造成越界
	if(wChnId >= NVR_MAX_CHN_NUM+2)
	{
		PRINTERR("NvrFixSnapPicSnapCB cap id::%d not ok\n",wChnId);
		OsApi_SemGive(g_hFixSnapSem);
		return;
	}
	PRINTDBG("NvrFixMultiSrcSnapCB nCapChn:%d  cap id:%d, eFixSnpEvt:%x\n",nCapChn,ptSnapCBInfo->dwContext,g_eFixMulitSrcSnpEvt[(u32)ptSnapCBInfo->dwContext]);
				
	///如果为邮件，则生成文件,由smtp用于邮件发送
	if(NVR_REC_SNP_EVT_MAIL == g_eFixMulitSrcSnpEvt[wChnId])
	{
		NvrFixSmtpSend(nCapChn,(char*)ptSnapCBInfo->tOrgPicInfo.pPicAddr, ptSnapCBInfo->tOrgPicInfo.dwPicLen);
	}
	else
	{
		if(MEDIACTRL_SNAP_STATUS_OK != ptSnapCBInfo->eOrgStatus)
		{
			PRINTERR("NvrFixSnapPicSnapCB manual snap OrgStatus :%d not ok\n",ptSnapCBInfo->eOrgStatus);
			OsApi_SemGive(g_hFixSnapSem);
			return;
		}

		NVRSTATUS eRet= NVR_ERR__OK;
		TNvrRecSnapPicInfo tSnapPicInfo;
		struct timeval tCurTime;
		mzero(tSnapPicInfo);

		gettimeofday(&tCurTime, NULL);
		NvrSysGetSystemTime(&tUtcTime);
		NvrSysTimeBdtUintSwitch(NVR_SYS_BDT_TO_UINT, (u32*)&tSnapPicInfo.qwTime, &tUtcTime);
		tSnapPicInfo.qwTime = tSnapPicInfo.qwTime*1000;
		
		tSnapPicInfo.eSnpEvt = g_eFixMulitSrcSnpEvt[(u32)ptSnapCBInfo->dwContext];
		tSnapPicInfo.wChnId = ptSnapCBInfo->dwContext;//dwContext 此处表示存放的通道id
		tSnapPicInfo.wHeight = ptSnapCBInfo->tOrgPicInfo.tPicRes.wHeight;
		tSnapPicInfo.wWidth = ptSnapCBInfo->tOrgPicInfo.tPicRes.wWidth;
		//tSnapPicInfo.qwTime = tCurTime.tv_sec*1000+tCurTime.tv_usec/1000;//image 生成时间,单位ms
		tSnapPicInfo.dwPicDataLen = ptSnapCBInfo->tOrgPicInfo.dwPicLen;
		tSnapPicInfo.pPicData = (s8 *)ptSnapCBInfo->tOrgPicInfo.pPicAddr;
		snprintf((s8*)tSnapPicInfo.achPicName, sizeof(tSnapPicInfo.achPicName), "%010lu.jpeg", tCurTime.tv_sec);//image名
		
		eRet = NvrRecSaveSnapPic(&tSnapPicInfo);
		if(NVR_ERR__OK == eRet)
		{
			PRINTDBG("NvrRecSaveSnapPic sucess eRet:%d\n", eRet);
		}
		else
		{
			PRINTERR("NvrRecSaveSnapPic failed eRet:%d\n",eRet);
		}
	}
	
	OsApi_SemGive(g_hFixSnapSem);
}

NVRSTATUS NvrFixSnapMultiSrcSnap(TNvrMpuSnapPictureParam tSnapPictureParam)
{
	NVRSTATUS eRet = NVR_ERR__OK;	
    s32 nRet = 0;
	TMediaCtrlPicSnapParam tParam;
	TNvrPuiVidEncParam tVidEncParam;
    mzero(tParam);	
    mzero(tVidEncParam);
	s32 s32BindEnc = 0;

	//获取对应encid
	eRet = LcamMcChnencId2MediactrlEncid(tSnapPictureParam.wChnId,tSnapPictureParam.wEncId,&s32BindEnc);
	if(NVR_ERR__OK != eRet)
	{
		PRINTERR("NvrFixSnapMultiSrcSnap LcamMcChnencId2MediactrlEncid Failed bySrcId:%d wEncId:%d\n",
			tSnapPictureParam.wChnId,tSnapPictureParam.wEncId);
		return eRet;
	}

	PRINTDBG("NvrFixSnapMultiSrcSnap  s32BindEnc:%d \n",s32BindEnc);
	
	eRet = NvrPuiGetDevParam(tSnapPictureParam.wChnId,tSnapPictureParam.wEncId,NVR_PUI_VID_ENC_PARAM,&tVidEncParam);
	if(NVR_ERR__OK != eRet)
	{
		PRINTERR("NvrFixSnapMultiSrcSnap NvrPuiGetDevParam Failed bySrcId:%d \n",tSnapPictureParam.wChnId);
		return eRet;
	}
	
	tParam.eSnapSource = MEDIACTRL_SNAP_FROM_ENCODE_STREAM;
    tParam.pFhr = NULL;
    tParam.ePicType = MEDIACTRL_PIC_JPEG;
    tParam.ePicQuality = MEDIACTRL_SNAP_QUALITY_HIGH;
    tParam.bNeedStop = TRUE;
    tParam.tPicSnapCB = NvrFixMultiSrcSnapCB;
    tParam.dwContext = tSnapPictureParam.wChnId;//存放抓拍的通道id
    tParam.pThumbnailsYuvAddr = NULL;
    tParam.tOrgPicRes.wHeight = tVidEncParam.wVidHeight;
    tParam.tOrgPicRes.wWidth = tVidEncParam.wVidWide;
	tParam.dwSnapBindStream = s32BindEnc;
	OsApi_SemTake(g_hFixSnapSem);
    nRet = MediaCtrlStartPicSnap(0, &tParam);
	if(0 == nRet)
	{
		if (FALSE == OsApi_SemTakeByTime(g_hFixSnapSem,3000))
		{
			PRINTERR("NvrFixSnapEncodeSnap failed time out\n");
			eRet = NVR_ERR__ERROR;
			OsApi_SemGive(g_hFixSnapSem);
		}
		else
		{
			PRINTDBG("NvrFixSnapEncodeSnap success\n");
			OsApi_SemGive(g_hFixSnapSem);
		}
	}
	else
	{
		PRINTERR("NvrFixSnapEncodeSnap failed\n");
		OsApi_SemGive(g_hFixSnapSem);
		eRet = NVR_ERR__ERROR;
	}
	return eRet;
}

NVRSTATUS NvrFixSnapEncodeSnap(u8 byEncId)
{
	NVRSTATUS eRet = NVR_ERR__OK;	
    s32 nRet = 0;
	TMediaCtrlPicSnapParam tParam;
	TNvrPuiVidEncParam tVidEncParam;
    mzero(tParam);	
    mzero(tVidEncParam);	

	NvrPuiGetDevParam(0,0,NVR_PUI_VID_ENC_PARAM,&tVidEncParam);
	PRINTDBG("NvrFixSnapEncodeSnap bySrcId:%d\n",byEncId);
    tParam.eSnapSource = MEDIACTRL_SNAP_FROM_ENCODE_STREAM;
    tParam.pFhr = NULL;
    tParam.ePicType = MEDIACTRL_PIC_JPEG;
    tParam.ePicQuality = MEDIACTRL_SNAP_QUALITY_HIGH;
    tParam.bNeedStop = TRUE;
    tParam.tPicSnapCB = NvrFixSnapPicSnapCB;
    tParam.dwContext = 100;
    tParam.pThumbnailsYuvAddr = NULL;
    tParam.tOrgPicRes.wHeight = tVidEncParam.wVidHeight;
    tParam.tOrgPicRes.wWidth = tVidEncParam.wVidWide;
	tParam.dwSnapBindStream = byEncId;
	OsApi_SemTake(g_hFixSnapSem);
    nRet = MediaCtrlStartPicSnap(0, &tParam);
	if(0 == nRet)
	{
		if (FALSE == OsApi_SemTakeByTime(g_hFixSnapSem,5000))
		{
			PRINTERR("NvrFixSnapEncodeSnap failed time out\n");
			eRet = NVR_ERR__ERROR;
		}
		else
		{
			PRINTDBG("NvrFixSnapEncodeSnap success\n");
			OsApi_SemGive(g_hFixSnapSem);
		}
	}
	else
	{
		PRINTERR("NvrFixSnapEncodeSnap failed\n");
		OsApi_SemGive(g_hFixSnapSem);
		eRet = NVR_ERR__ERROR;
	}
	return eRet;
}

void  NvrOnvifProSnapFixCB(ENvrMpuSpecialDataType eType,void* pData)
{
	TNvrMpuSnapPictureParam tSnapPictureParam;
	mzero(tSnapPictureParam);

	if(NVR_MPU_SPECIAL_TYPE_PTZ_SNAP == eType)
	{
		memcpy(&tSnapPictureParam,pData,sizeof(TNvrMpuSnapPictureParam));

		g_eFixSnpEvt = tSnapPictureParam.eSnpEvt;
		PRINTERR("NvrOnvifProSnapFixCB g_eFixSnpEvt %x\n",g_eFixSnpEvt);
		
		NvrFixSnapEncodeSnap(tSnapPictureParam.wChnId);
	}
}

void  NvrMultiSrcProSnapFixCB(ENvrMpuSpecialDataType eType,void* pData)
{
	TNvrMpuSnapPictureParam tSnapPictureParam;
	mzero(tSnapPictureParam);

	if(NVR_MPU_SPECIAL_TYPE_PTZ_SNAP == eType)
	{
		memcpy(&tSnapPictureParam,pData,sizeof(TNvrMpuSnapPictureParam));
		g_eFixMulitSrcSnpEvt[tSnapPictureParam.wChnId] = tSnapPictureParam.eSnpEvt;
		
		PRINTDBG("NvrMultiSrcProSnapFixCB wChnId:%d wEncId:%d SnpEvt%x\n",tSnapPictureParam.wChnId,tSnapPictureParam.wEncId,tSnapPictureParam.eSnpEvt);
		NvrFixSnapMultiSrcSnap(tSnapPictureParam);
	}
}
void  NvrAirpMultiSnapFixCB(u16 wChnId, u16 wEncId, void* pData)
{
	s32 nRet = 0;
	s32 s32BindEnc = 0;
	TMediaCtrlPicSnapParam tSnapPictureParam;
	mzero(tSnapPictureParam);
	memcpy(&tSnapPictureParam,pData,sizeof(TMediaCtrlPicSnapParam));

	//获取对应encid
	nRet = LcamMcChnencId2MediactrlEncid(wChnId, wEncId,&s32BindEnc);
	if(NVR_ERR__OK != nRet)
	{
		PRINTERR("NvrAirpMultiSnapFixCB LcamMcChnencId2MediactrlEncid Failed bySrcId:%d wEncId:%d\n",
				wChnId,wEncId);
		return;
	}
	
	PRINTDBG("NvrAirpMultiSnapFixCB  s32BindEnc:%d \n",s32BindEnc);	
	tSnapPictureParam.dwSnapBindStream = s32BindEnc;
	nRet = MediaCtrlStartPicSnap(0, &tSnapPictureParam);

}

void  NvrFixCoreSysRecovCB(TNvrSysRecoverParam *ptRecoveryParam)
{
    TNvrCapSysInfo tCapSys;
    TPigeonAppCfgInfo tPigeonCfg;
    TNvrSysDevInfo tDevInfo;
    TPdnsAppCfg tPdnsCfg;
    EAppResult eAppRet = APP_OK;
    EAppResult eShareRet = APP_OK;
    
    mzero(tCapSys);
    mzero(tPigeonCfg);
    mzero(tDevInfo);
    mzero(tPdnsCfg);

    NvrCapGetCapParam(NVR_CAP_ID_SYS, &tCapSys);

    ///<完全恢复 
    if(ptRecoveryParam->dwRecoverCfgType & (0x01 << NVR_CFG_RESET_ALL))
    {
        ///<清除生产测试标记位
        s32 nRet = BrdPinfoFlag0Ops(FL0_TST_CLEAR,0);
        PRINTDBG("clear product test flags %d \n", nRet);

        //云台恢复出厂
        if (NVR_CAP_SUPPORT == tCapSys.tUpgradePtz.bSupUpgrade)
        {
            NvrFixDevPtzFactoryReset();
        }

        ///< 删除断电记忆文件
        remove(LCAM_DEV_POWEROFFRSM_CFG_PATH);
    }

    NvrSysGetDevInfo(&tDevInfo);

    //吴江331告警定制特殊处理
    if(0x1893 == tDevInfo.dwPid && (ptRecoveryParam->dwRecoverCfgType & (0x01 << NVR_INTELL_CFG_RESET)))
    {
        tPigeonCfg.bEnable = TRUE;
        strncpy(tPigeonCfg.achPlatAddr, "*************", sizeof(tPigeonCfg.achPlatAddr));
        tPigeonCfg.wPlatport = 8888;
        strncpy(tPigeonCfg.achUnitName,"department",sizeof(tPigeonCfg.achUnitName));
		strncpy(tPigeonCfg.achUnitNumber,"88888888",sizeof(tPigeonCfg.achUnitNumber));
		strncpy(tPigeonCfg.achUserName,"user",sizeof(tPigeonCfg.achUserName));
		strncpy(tPigeonCfg.achUserNumber,"88888888",sizeof(tPigeonCfg.achUserNumber));   
        eAppRet = AppRunShareAction(PIGEONAPP_SET_CFG,  &tPigeonCfg, NULL, NULL, &eShareRet);
        if(APP_OK != eAppRet || APP_OK != eShareRet)
        {
            PRINTERR("set pigeonapp cfg fail, %d, %d\n", eAppRet, eShareRet);
        }
        
        tPdnsCfg.bEnable = TRUE;
        strncpy(tPdnsCfg.achServerIP,"*************",sizeof(tPdnsCfg.achServerIP));
        tPdnsCfg.wServerPort = 4502;
        tPdnsCfg.bPdnsUserInf = FALSE;
        strncpy(tPdnsCfg.achAppID, "00000000", sizeof(tPdnsCfg.achAppID));
        strncpy(tPdnsCfg.achAppKey, "00000000", sizeof(tPdnsCfg.achAppKey));
        strncpy(tPdnsCfg.achName, "", sizeof(tPdnsCfg.achName));
        eAppRet = AppRunShareAction(PDNSAPP_SET_CFG, &tPdnsCfg, NULL, NULL, &eShareRet);
        if(APP_OK != eAppRet || APP_OK != eShareRet)
        {
            PRINTERR("set pdns cfg fail, %d, %d\n", eAppRet, eShareRet);
        }
    }
        

    ///< 2022-08-25  去除恢复配置需要清除isp af 配置逻辑=====》  测试gezhihai和gezhiming 确认，要求恢复出厂不删除af配置文件
    ///< NvrSystem("rm -rf /usr/config/IspSavedParamValue_Isp*");
}


NVRSTATUS NvrFixLcamMessageEventCb(ELcamSpecialEventType eEventType,u16 wChnId,void *ptDataBuf)
{
	NVRSTATUS eRet = NVR_ERR__OK;

	PRINTDBG("eEventType %u, wChnId %u\n ",eEventType,wChnId);

	return eRet;
}

void NvrFixCheckTaskCB(TNvrGuardCBParam *pCBParam)
{
	NvrFixIspLightDayNightCheckDeal();
}

void NvrFixCheckTaskInit()
{
	TNvrGuardTaskParam tTaskParam;
	
	mzero(tTaskParam);

    strcpy(tTaskParam.achTaskName,"NvrFixCheckTaskCB");
    tTaskParam.bEnable = TRUE;
    tTaskParam.dwCheckTime = 1;
    tTaskParam.pfGuardDealCB = NvrFixCheckTaskCB;   ///探针上传检测接口
    NvrGuardTaskRegist(&tTaskParam);    
}

//ssc339g平台固件升级
static void NvrFixFirmwareUpdate()
{
#ifdef _SSC339G_
    const s8 *ps8IplFilePath = "/usr/bin/IPL.bin";
	const s8 *ps8IplCustFilePath = "/usr/bin/IPL_CUST.bin";
    u8 u8Index = 0, u8RebootFlag = 0;
    
    if(access(ps8IplFilePath, F_OK) == 0)
    {
        for(u8Index = 0; u8Index < 3; u8Index++)
        {
            if(0 == SysFirmwareUpdate(ps8IplFilePath, 1))
            {
                NvrFixLog("u8Index[%d]: SysFirmwareUpdate[%s] Success !!!\r\n", u8Index, ps8IplFilePath);
                break;
            }
            else
            {
                NvrFixLog("u8Index[%d]: SysFirmwareUpdate[%s] Failed !!!\r\n", u8Index, ps8IplFilePath);
            }
        }
        u8RebootFlag = 1;
    }
    else
    {
        NvrFixLog("[%s] not exist !!!\r\n", ps8IplFilePath);
    }

    if(access(ps8IplCustFilePath, F_OK) == 0)
    {
        for(u8Index = 0; u8Index < 3; u8Index++)
        {
            if(0 == SysFirmwareUpdate(ps8IplCustFilePath, 2))
            {
                NvrFixLog("u8Index[%d]: SysFirmwareUpdate[%s] Success !!!\r\n", u8Index, ps8IplCustFilePath);
                break;
            }
            else
            {
                NvrFixLog("u8Index[%d]: SysFirmwareUpdate[%s] Failed !!!\r\n", u8Index, ps8IplCustFilePath);
            }
        }
        u8RebootFlag = 1;
    }
    else
    {
        NvrFixLog("[%s] not exist !!!\r\n", ps8IplCustFilePath);
    }

    //重启
    if(u8RebootFlag)
    {
        NvrFixLog("SysHwReset sys will be reboot !!!\r\n");
        SysHwReset(1);
    }
#endif
}

static void NvrFixSysNotifyPlatState(ENvrSysNotifyPlatStateType eType, void *pContext)
{
    if(NULL == pContext)
    {
        NvrFixLog("pContext = NULL !!!\r\n");
        return;
    }
    
    TNvrSysNotifyPlatInitInfo *ptInitInfo = (TNvrSysNotifyPlatInitInfo *)pContext;

    if(NVR_SYS_NOTIFY_PLAT_TYPE_INIT == eType && ptInitInfo->bSuc)
    {
#ifdef _SSC339G_
        s8 as8CusVersion[NVR_FIX_DEV_MAX_SOFTVER_LEN] = {0};
        s32 nDefaultVer = 20220000;   //获取的版本号是无效值 设置成一个默认值
        s32 nPkgFwVerNum = 20220701;  //pkg里面要升级的固件版本号 (20220701 对应 20230613 这个版本，解决坏块导致无法进入boot)
        s32 nCurFwVerNum = 0;

        if(0 == SysGetFwVerNum(0, (u32 *)&nCurFwVerNum))
        {
            sprintf(as8CusVersion, "%d", (s32)nCurFwVerNum);
            //判断版本号是否有效
            if(as8CusVersion[0] == '2' && as8CusVersion[1] == '0' && as8CusVersion[2] == '2'/* &&  as8CusVersion[3] == '2'*/)
            {
                NvrSysSetClipVer(as8CusVersion);
            }
            else
            {
                nCurFwVerNum = nDefaultVer;
                sprintf(as8CusVersion, "%d", (s32)nCurFwVerNum);
                NvrSysSetClipVer(as8CusVersion);
            }

            if(nCurFwVerNum < nPkgFwVerNum)
            {
                NvrFixLog("nCurFwVerNum[%d] < nPkgFwVerNum[%d] !!!\r\n", nCurFwVerNum, nPkgFwVerNum);
                NvrFixFirmwareUpdate();
            }
        }
#endif
        do
        {
            TNvrCapLcam tCapLcam;

            mzero(tCapLcam);
            NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);

            if(NVR_CAP_SUPPORT == tCapLcam.tLcamMcInfo.tDevWorkMode[0].bMonitorModeSup)
            {
                u32 dwCfgSysMemSize = COMMON_MODE_SYSMEM_SIZE;
                u32 dwRealSysMemSize = 0;
                TLcamMcWorkMode tWorkMode;

                LcamMcGetWorkModeCfgChn(0,&tWorkMode);
#ifdef _SSC339G_
                TNvrCapFixInernalCapInfo tFixInterCapInfo;
                NvrCapGetFixInterCapParam(&tFixInterCapInfo);
                if(0 == tFixInterCapInfo.tFixHwInternalCap.byExtPlat)
                {
                    //SSC339G不支持动态修改内存分区
                    ///监控模式下 进行结构化算法和基础智能算法切换
                    if(tFixInterCapInfo.tFixProIntelCap.bySupAccordToMonitorsModeSwitchAlg)
                    {
                        if(NVR_MONITOR_MODE_COMMON == tWorkMode.eMonitorMode)
                        {
                            tCapLcam.tLcamMcInfo.bySupNotInitIntelliAlg = NVR_CAP_SUPPORT;
                        }
                        else if(NVR_MONITOR_MODE_INTELLI == tWorkMode.eMonitorMode)
                        {
                            tCapLcam.tLcamMcInfo.bySupNotInitIntelliAlg = NVR_CAP_NONSUPPORT;
                        }

                        NvrCapSetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);
                    }
                    break;
                }
                dwCfgSysMemSize = NVR_MONITOR_MODE_INTELLI == tWorkMode.eMonitorMode ? INTELLI_MODE_SYSMEM_SIZE : COMMON_MODE_SYSMEM_SIZE;

                dwRealSysMemSize =  NvrFixCoreParseCmdlineSysMemInfo();
                NvrFixLog("monitor mode sys mem not equal "FORMAT_HEX"(cfg) "FORMAT_HEX"(real) \n", dwCfgSysMemSize, dwRealSysMemSize);
                if (dwCfgSysMemSize != dwRealSysMemSize)
                {
                    BOOL bReboot = TRUE;
                    s8 abyBuff[32];

                    SysMemModify(dwCfgSysMemSize);

                    do
                    {
                        FILE *fp;
                        u8 byRebootCnt = 0;

                        if(NULL == (fp = fopen("/usr/config/reboot_cnt", "r")))
                        {
                            PRINTERR("fopen reboot_cnt err \n");
                            break;
                        }

                        mzero(abyBuff);
                        if (NULL != fgets(abyBuff, 31, fp))
                        {
                            byRebootCnt = atoi(abyBuff);
                        }
                        fclose(fp);

                        mzero(abyBuff);
                        snprintf(abyBuff, sizeof(abyBuff) - 1, "re_cnt:%d", byRebootCnt);
                        if (byRebootCnt > 5)
                        {
                            bReboot = FALSE;
                            break;
                        }

                    }while (0);

                    NvrFixLog("monitor mode sys mem not equal "FORMAT_HEX"(cfg) "FORMAT_HEX"(real) and %s\n", dwCfgSysMemSize, dwRealSysMemSize, bReboot ? "reboot" : abyBuff);

                    if (bReboot)
                    {
                        NvrSysReboot(NULL);
                    }
                }
#endif
            }
        }while (0);
    }
}

//事件告警信息统计结构体
typedef struct
{
    u32 dwMotionEventNum;           //移动侦测事件上报统计
    u32 dwObjTypeNormalNum;           //非移动侦测事件目标类型正常统计
    u32 dwObjTypeAbnormNum;           //非移动侦测事件目标类型异常统计
    u32 dwAlarmInfoPushNum;         //告警信息进栈统计
    u32 dwAlarmInfoPushFailedNum;
    u32 dwAlarmInfoPopNum;          //告警信息出栈统计
    u32 dwAlarmInfoPopFailedNum;
    u32 dwCoiSupportNum;            //COI支持统计
    u32 dwCoiSupportFailedNum;
    u32 dwPicSnapNum;               //图片抓拍统计
    u32 dwPicSnapFailedNum;
    u32 dwPicPathOpenNum;              //图片打开统计
    u32 dwPicPathOpenFailedNum;
    u32 dwCoiReportNum;             //COI数据上报统计
    u32 dwCoiReportFailedNum;
    s32 dwPicWriteSucNum;          //图片存储成功统计
    s32 dwPicWriteFailNum;          //图片存储失败统计
    s32 dwPicNotExistNum;
    s32 dwPicDelTotalNum;           //图片删除统计
    u32 dwPicDelFailedTotalNum;
    u32 dwContextErrNum;            //抓拍回调上下文判断异常统计
    u32 dwSnapIndexErrNum;          //抓拍索引号判断异常统计
}TEventCoiDbgInfo;

//事件告警COI上报句柄
static TItEventCoiHandle g_tEventCoiHandle = { 0 };
static TEventCoiDbgInfo g_tEventCoiDbgInfo = { 0 };

//存储图片
static NVRSTATUS NvrFixCoiSaveJpeg(s8 achPicPath[NVR_MAX_STR256_LEN], u8* pbyVirAddr, u32 dwFrameSize)
{
    FILE *pFd = NULL;
    BOOL bSaveFLag = FALSE;
    //判断设备是否存在snap目录，不存在则创建
    if(0 != access(NVR_SYNC_SNAPPIC_COI_PATH, F_OK))
    {
        mkdir(NVR_SYNC_SNAPPIC_COI_PATH, S_IRWXU);
    }

    if((pFd = fopen(achPicPath, "wb")))
    {
        g_tEventCoiDbgInfo.dwPicPathOpenNum++;
        if(fwrite(pbyVirAddr, dwFrameSize, 1, pFd))
        {
            g_tEventCoiDbgInfo.dwPicWriteSucNum++;
            bSaveFLag = TRUE;
        }
        else
        {
            g_tEventCoiDbgInfo.dwPicWriteFailNum++;
            bSaveFLag = FALSE;
        }
        fclose(pFd);
    }
    else
    {
        g_tEventCoiDbgInfo.dwPicPathOpenFailedNum++;
        bSaveFLag = FALSE;
    }

    if(bSaveFLag)
    {
        return NVR_ERR__OK;
    }
    else
    {
        return NVR_ERR__ERROR;
    }
}
static u8 *g_pPicAddr = NULL;

#ifdef _SSC339G_
NVRSTATUS NvrFixSnapInfoReportPubsec(TAisUpdFileInfo *ptUpdFile, TBasicIntelliSnapOutput *ptSnapOut)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 ePubRet = 0;
    EAisDataUploadType eUploadType = AIS_DATA_UPLOAD_TYPE_PUBSEC1;
    TPubSecIntelliPicObj *ptPubPicObj = NULL;
    TAisPubSecIntelliImgFaceObj tImgFace;
    static char *g_pchPicData = NULL;
	u32 dwDataLen = 0;
	static char *g_pchBase64Data = NULL;
	s32 nBase64Len = 0;

    mzero(tImgFace);
    
    TAisUpdDataContext *ptUpdContext = (TAisUpdDataContext *)NVRALLOC(sizeof(TAisUpdDataContext));
    if (ptUpdContext)
    {
        memset(ptUpdContext, 0, sizeof(TAisUpdDataContext));
        ptUpdContext->qwFileId = ptUpdFile->qwFileId;
        ptUpdContext->eDataType = ptUpdFile->eDataType;
        ptUpdContext->wChnId = ptUpdFile->wChnId;
        ptUpdContext->eFileType = ptUpdFile->eFileType;
        memcpy(ptUpdContext->aeUploadType, ptUpdFile->aeUploadType, sizeof(ptUpdContext->aeUploadType));
        // PRINTDBG("ptUpdContext malloc %p \n", ptUpdContext);
    }

    do
    {
        ///<预先分配2M内存--图片大小
        dwDataLen = 300*1024;
        if (!g_pchPicData)
        {
            g_pchPicData = (char *)NVRALLOC(dwDataLen);
            if(NULL == g_pchPicData)
            {
                PRINTERR("pchPicData malloc err !!\n");
                eRet = NVR_ERR__ERROR;
                break;
            }
        }

        ptPubPicObj = (TPubSecIntelliPicObj *)NVRALLOC(sizeof(TPubSecIntelliPicObj));
        if(NULL == ptPubPicObj)
        {	
            eRet = NVR_ERR__ERROR;
            PRINTERR("malloc TPubSecIntelliPicObj failed !!\n");
            break;
        }
        memset(ptPubPicObj, 0, sizeof(TPubSecIntelliPicObj));

        ptPubPicObj->ptImgFace = (TPubSecIntelliImgFaceObj *)NVRALLOC(sizeof(TPubSecIntelliImgFaceObj));
        if(NULL == ptPubPicObj->ptImgFace)
        {	
            eRet = NVR_ERR__ERROR;
            PRINTERR("malloc ptPubPicObj->ptImgFace failed !!\n");
            break;
        }
        memset(ptPubPicObj->ptImgFace, 0, sizeof(TPubSecIntelliImgFaceObj));


        ptPubPicObj->ptImgFace->dwFaceNum = 1;
        ptPubPicObj->ptImgFace->aptPubSecFaceObj = (TPubSecFaceObjInfo *)NVRALLOC(sizeof(TPubSecFaceObjInfo));
        if(NULL == ptPubPicObj->ptImgFace->aptPubSecFaceObj)
        {	
            eRet = NVR_ERR__ERROR;
            PRINTERR("malloc ptPubPicObj->ptImgFace->aptPubSecFaceObj failed !!\n");
            break;
        }
        memset(ptPubPicObj->ptImgFace->aptPubSecFaceObj, 0, sizeof(TPubSecFaceObjInfo));

        ptPubPicObj->ptImgFace->aptPubSecFaceObj->nSubImageNum = 1;
        ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage  = (TPubSecSubImageList *)NVRALLOC(sizeof(TPubSecSubImageList));
        if(NULL == ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage )
        {	
            eRet = NVR_ERR__ERROR;
            PRINTERR("malloc TPubSecSubImageList failed !!\n");
            break;
        }
        memset(ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage, 0, sizeof(TPubSecSubImageList));

        ///<基础字段填写
        tImgFace.dwFaceNum = 1;
        tImgFace.tPubSecFaceObj[0].bVaild = TRUE;
        tImgFace.tPubSecFaceObj[0].dwTime =  ptUpdFile->qwStartTime;
        tImgFace.tPubSecFaceObj[0].wStartX = 0;
        tImgFace.tPubSecFaceObj[0].wStartY = 0;
        tImgFace.tPubSecFaceObj[0].wWidth = ptSnapOut->s16ImgW;
        tImgFace.tPubSecFaceObj[0].wHeight = ptSnapOut->s16ImgH;
                    
        tImgFace.tPubSecFaceObj[0].dwSubFaceImgNum = 1;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].dwFileId = ptUpdFile->qwFileId;
        memcpy(tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].achFilePath, ptUpdFile->achFilePath, sizeof(tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].achFilePath));
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].eSubImgType = AIS_PUBSEC_INTELLI_SUBIMG_FACE;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].ePicType = AIS_PUBSECINTELLI_PIC_JPEG;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].dwTime = ptUpdFile->qwStartTime;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].dwFileSize = ptSnapOut->u32JpgBytes;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].wWidth = ptSnapOut->s16ImgW;
        tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0].wHeight = ptSnapOut->s16ImgH;

        _AisUpdPubSecGetFaceInf(eUploadType, ptPubPicObj->ptImgFace->aptPubSecFaceObj, &tImgFace.tPubSecFaceObj[0]);
        _AisUpdPubSecGetSubInf(eUploadType, ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage, &tImgFace.tPubSecFaceObj[0].tFaceObjSubImg[0]);

        nBase64Len = dwDataLen*2;
        // PRINTDBG("malloc g_pchBase64Data, nBase64Len size:%u!!\n", nBase64Len);
        if (!g_pchBase64Data)
        {
            g_pchBase64Data = (char *)NVRALLOC(nBase64Len);
            if (!g_pchBase64Data)
            {
                PRINTERR("g_pchBase64Data err!\n");
                eRet = NVR_ERR__ERROR;
                break;
            }
        }
        
        ///<图片数据转化为base64格式
        ePubRet = PubSecStackBase64Encode(g_pPicAddr, (s32)ptSnapOut->u32JpgBytes, g_pchBase64Data, nBase64Len);
        if(PUBSEC_CLT_OK != ePubRet)
        {
            PRINTERR("PubSecStackBase64Encode err, ret=%d !!\n", ePubRet);
            eRet = NVR_ERR__ERROR;
            break;
        }

        ptPubPicObj->ptImgFace->aptPubSecFaceObj[0].ptFaceSubImage[0].pSubImageBase64BinaryData = g_pchBase64Data;

        AisUpdCallBackPubsecReport(AIS_DATA_UPLOAD_TYPE_PUBSEC1, PUBSEC_INTELLI_DATA_PIC_FACE, ptPubPicObj, ptUpdContext);

        return eRet;
    }while (0);
    
    if (ptUpdContext)
    {
        NVRFREE(ptUpdContext);
    }
    if (ptPubPicObj)
    {
        if (ptPubPicObj->ptImgFace)
        {
            if ( ptPubPicObj->ptImgFace->aptPubSecFaceObj)
            {
                if (ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage )
                {
                    NVRFREE(ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage);
                }
                NVRFREE( ptPubPicObj->ptImgFace->aptPubSecFaceObj);
            }
            NVRFREE(ptPubPicObj->ptImgFace);
        }
        NVRFREE(ptPubPicObj);
    }
    return eRet;
}

NVRSTATUS NvfFixPubResultAchieve(EAisDataUploadType eUploadType, EAisUploadDataType eUploadDataType, void* pInfoIn, void* pParamIn, void* pResultIn, void* pContext)
{
	EAisUploadResult eResult = AIS_UPLOAD_RESULT_COUNT;
	eResult = (u32)pResultIn;
    // PRINTDBG("NvfFixPubResultAchieve, UploadType:[%d], eUploadDataType:[%d], result:%d, pData:%p, pvContext:%p!!\n",  eUploadType, eUploadDataType, eResult, pParamIn, pContext);

	if(AIS_UPLOAD_DATA_PIC_FACE_SNAP == eUploadDataType )
	{
        if (pContext)
        {
            NVRFREE(pContext);
        }
        if(pParamIn)
        {
            TPubSecIntelliPicObj *ptPubPicObj = (TPubSecIntelliPicObj *)pParamIn;
            if (ptPubPicObj)
            {
                if (ptPubPicObj->ptImgFace)
                {
                    if ( ptPubPicObj->ptImgFace->aptPubSecFaceObj)
                    {
                        if (ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage )
                        {
                            NVRFREE(ptPubPicObj->ptImgFace->aptPubSecFaceObj->ptFaceSubImage);
                        }
                        NVRFREE( ptPubPicObj->ptImgFace->aptPubSecFaceObj);
                    }
                    NVRFREE(ptPubPicObj->ptImgFace);
                }
                NVRFREE(ptPubPicObj);
            }
        }
	}
	else
	{
        PRINTDBG("this type not to do ..... \n");
	}

    MAKE_COMPILER_HAPPY(eResult);

	return NVR_ERR__OK;
}
#endif

//COI事件上报处理
static void NvrFixEventDealForCoi(TAlarmEventInfo *ptAlarmEventInfo, u8 *pPicAddr, TBasicIntelliSnapOutput *ptSnapOut, TNvrBrokenDownTime *ptEventSysTime, TPicPathParam *ptPicPathParam)
{
    TEdgeCoiEventInfo tAlarmEventInfoCoi;
    TEdgeCoiParam tCoiParam;
    static u32 dwImageCount = 0;

    mzero(tAlarmEventInfoCoi);
    mzero(tCoiParam);

    //COI上报信息填写
    //事件码设置
    
    //事件类型
    if(ptAlarmEventInfo->eEventType == NVR_ALARM_TYPE_SMART)
    {
        if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_ENTER_AREA)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-11", sizeof(tAlarmEventInfoCoi.achEventCode)); //区域进入
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_LEAVE_AREA)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-12", sizeof(tAlarmEventInfoCoi.achEventCode)); //区域离开
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_AREA_INVADE)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-13", sizeof(tAlarmEventInfoCoi.achEventCode)); //区域入侵
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_WARNINGLINE)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-14", sizeof(tAlarmEventInfoCoi.achEventCode)); //警戒线
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_USER_DEF_1)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-15", sizeof(tAlarmEventInfoCoi.achEventCode)); //烟火检测
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_THERMEASTEMP)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-16", sizeof(tAlarmEventInfoCoi.achEventCode)); //高温告警
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_LEAVEBEHIND)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-18", sizeof(tAlarmEventInfoCoi.achEventCode)); //物品遗留
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_TAKEAWAY)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-19", sizeof(tAlarmEventInfoCoi.achEventCode)); //物品拿取
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_SHIELD_DETECDT)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-20", sizeof(tAlarmEventInfoCoi.achEventCode)); //遮挡检测
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_VITURAL_FOCUS)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-21", sizeof(tAlarmEventInfoCoi.achEventCode)); //虚焦检测
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_SCENE_CHANGE)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "03-22", sizeof(tAlarmEventInfoCoi.achEventCode)); //场景变更
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_GATHER)               
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "08-02", sizeof(tAlarmEventInfoCoi.achEventCode)); //人员聚集
        }
        else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_FACE_DETECT)
        {
            strncpy(tAlarmEventInfoCoi.achEventCode, "07-02", sizeof(tAlarmEventInfoCoi.achEventCode)); //人脸检测
        }
        else
        {
            PRINTERR("Unknown EventType :%d!!!\r\n",ptAlarmEventInfo->eAlmSDType);
            return;
        }
    }
    else if(ptAlarmEventInfo->eEventType == NVR_ALARM_TYPE_MD)
    {
        strncpy(tAlarmEventInfoCoi.achEventCode, "03-10", sizeof(tAlarmEventInfoCoi.achEventCode));//移动侦测
    }
    
    //目标类型
    if(ptAlarmEventInfo->nCapChn == 1 && (ptAlarmEventInfo->eAlmSDType != NVR_ALARM_SD_USER_DEF_1 && ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_THERMEASTEMP))
    {
        AppStrncat(tAlarmEventInfoCoi.achEventCode, "-00", sizeof(tAlarmEventInfoCoi.achEventCode)); //热成像通道其他类型处理成00(烟火和测温除外)
    }
    else if(ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_FACE_DETECT || ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_SHIELD_DETECDT
    || ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_VITURAL_FOCUS|| ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_SCENE_CHANGE
    || ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_LEAVEBEHIND || ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_TAKEAWAY
    || ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_GATHER)
    {
         
    }
    else
    {
        //多个异常运动目标类型时，先默认使用第0个
        if(ptAlarmEventInfo->atAbnormTargets[0].eObjType == MEDIACTRL_OBJTYPE_PED)
        {
            AppStrncat(tAlarmEventInfoCoi.achEventCode, "-01", sizeof(tAlarmEventInfoCoi.achEventCode)); //行人
        }
        else if(ptAlarmEventInfo->atAbnormTargets[0].eObjType == MEDIACTRL_OBJTYPE_VEH)
        {
            AppStrncat(tAlarmEventInfoCoi.achEventCode, "-02", sizeof(tAlarmEventInfoCoi.achEventCode)); //车辆
        }
        else if(ptAlarmEventInfo->atAbnormTargets[0].eObjType == MEDIACTRL_OBJTYPE_NONMOTOR)
        {
            AppStrncat(tAlarmEventInfoCoi.achEventCode, "-03", sizeof(tAlarmEventInfoCoi.achEventCode)); //非机动车
        }
        else if(ptAlarmEventInfo->atAbnormTargets[0].eObjType == MEDIACTRL_OBJTYPE_SHIP)
        {
            AppStrncat(tAlarmEventInfoCoi.achEventCode, "-04", sizeof(tAlarmEventInfoCoi.achEventCode)); //船
        }
        else
        {
            AppStrncat(tAlarmEventInfoCoi.achEventCode, "-00", sizeof(tAlarmEventInfoCoi.achEventCode)); //其他类型处理成00
        }
    }

    dwImageCount++;
    //服务器消息流水号：抓拍时间
    snprintf(tAlarmEventInfoCoi.achMsgID, sizeof(tAlarmEventInfoCoi.achMsgID), 
                    "%04u%02u%02u%02u%02u%02u"FORMAT_U32"", 
                    ptEventSysTime->wYear, ptEventSysTime->byMonth, ptEventSysTime->byDay, 
                    ptEventSysTime->byHour, ptEventSysTime->byMinute, ptEventSysTime->bySecond, dwImageCount);

    //事件描述：通道号
    snprintf(tAlarmEventInfoCoi.achExInfo, sizeof(tAlarmEventInfoCoi.achExInfo), 
                    "{\"videoid\":"FORMAT_U32"}", (u32)(ptAlarmEventInfo->nCapChn));

    // COI上报其他信息
    strncpy(tAlarmEventInfoCoi.achMsgType, "EVENT_LISTENING", sizeof(tAlarmEventInfoCoi.achMsgType)); // COI消息类型
    tAlarmEventInfoCoi.dwEventTime = ptPicPathParam->dwEventTime;                                       // 事件发生时间
    tAlarmEventInfoCoi.tEventInfo.tPersonInfo.byImageNum = 1;                                         // 图片个数
    if (ptAlarmEventInfo->eAlmSDType == NVR_ALARM_SD_FACE_DETECT)
    {
        tAlarmEventInfoCoi.tEventInfo.tPersonInfo.tImages->byImageType = 1; // 图片类型场景图
    }
    else
    {
        tAlarmEventInfoCoi.tEventInfo.tPersonInfo.tImages->byImageType = 15; // 图片类型场景图
    }

    tAlarmEventInfoCoi.tEventInfo.tPersonInfo.tImages->byImageFileFormat = 2; //图片格式base64

    //抓拍编码图片文件名
    snprintf(g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum].achPicPath, 
                sizeof(g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum].achPicPath), "%s", ptPicPathParam->achPicPath);
    strncpy(tAlarmEventInfoCoi.tEventInfo.tPersonInfo.tImages->achHeadPortrait, 
                g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum].achPicPath, 
                sizeof(tAlarmEventInfoCoi.tEventInfo.tPersonInfo.tImages->achHeadPortrait)-1);

    tCoiParam.pData = (void *)&tAlarmEventInfoCoi;
    tCoiParam.dwDataLen = sizeof(TEdgeCoiEventInfo);
    if (NVR_ERR__OK == EdgeCoiServiceMsgDataNotify(EDGE_COI_EVENT_LISTENING, &tCoiParam, NULL, NULL))
    {
        g_tEventCoiDbgInfo.dwCoiReportNum++;
    }
    else
    {
        g_tEventCoiDbgInfo.dwCoiReportFailedNum++;
        PRINTERR("EdgeCoiServiceMsgDataNotify Failed !!!\r\n");
    };

    return;
}

//pubsec上报处理
static void NvrFixEventDealForPubsec(TAlarmEventInfo *ptAlarmEventInfo, u8 *pPicAddr, TBasicIntelliSnapOutput *ptSnapOut, TNvrBrokenDownTime *ptEventSysTime, TPicPathParam *ptPicPathParam)
{
#ifdef _SSC339G_
    s32 eRet = 0;
    TAisUpdFileInfo tUpdFile;

    mzero(tUpdFile);

    tUpdFile.wChnId = ptAlarmEventInfo->nCapChn;
    tUpdFile.qwFileId = (ptEventSysTime->bySecond * 1000 + ptEventSysTime->dwMsec);
    tUpdFile.eFileType = AIS_UPLOAD_FILE_FACEPIC_SMALL;
    tUpdFile.qwStartTime = ptPicPathParam->dwEventTime;
    memcpy(tUpdFile.achFilePath, ptPicPathParam->achPicPath, sizeof(tUpdFile.achFilePath)); 
    tUpdFile.eSrctype = AIS_UPD_FILE_SOURCE_TYPE_REAL_TIME;
    tUpdFile.eDataType = AIS_UPLOAD_DATA_PIC_FACE_SNAP;

    eRet = NvrFixSnapInfoReportPubsec(&tUpdFile, ptSnapOut);
    if(NVR_ERR__OK != eRet)
    {
        PRINTERR("NvrFixSnapInfoReportPubsec ERR, eDataType:%d, ret=%d !!\n", tUpdFile.eDataType, eRet);
        return;
    }
#endif

    return;
}

//事件告警信息处理
static void NvrFixReportEventDeal(TAlarmEventInfo *ptAlarmEventInfo, u8 *pPicAddr, TBasicIntelliSnapOutput *ptSnapOut)
{
    TNvrBrokenDownTime tEventSysTime;
    TPicPathParam tPicPathParam;

    //入参安全检查
    if (NULL == ptSnapOut || NULL == ptAlarmEventInfo || NULL == pPicAddr)
    {
        PRINTERR("ptSnapCBInfo param error !!!\r\n");
        return;
    }
    if(ptSnapOut->s16ImgH == 0 || ptSnapOut->s16ImgW == 0)
    {
        PRINTERR("pic is empty !!!\r\n");
        return;
    }

    mzero(tEventSysTime);
    mzero(tPicPathParam);

    //获取本地系统时间
    NvrSysGetSystemLocalTime(&tEventSysTime);

    //图片存储锁
    pthread_mutex_lock(&(g_tEventCoiHandle.nPicLock));
    
    if(g_tEventCoiHandle.dwCurPicNum >= IT_PIC_SAVE_MAX_NUM)
    {
        PRINTERR("g_tEventCoiHandle.dwPicSaveNum[%d] >= %d !!!\r\n", IT_PIC_SAVE_MAX_NUM);
        pthread_mutex_unlock(&(g_tEventCoiHandle.nPicLock));
        return;
    }
    
    g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum].dwEventTime = NvrSysGetCurTimeMSec();
    tPicPathParam = g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum];

    snprintf(tPicPathParam.achPicPath, sizeof(tPicPathParam.achPicPath), 
        NVR_SYNC_SNAPPIC_COI_PATH"%04u%02u%02u%02u%02u%02u"FORMAT_U32"_%ux%u.jpg", tEventSysTime.wYear, tEventSysTime.byMonth, tEventSysTime.byDay, tEventSysTime.byHour, tEventSysTime.byMinute, tEventSysTime.bySecond, tEventSysTime.dwMsec, ptSnapOut->s16ImgH, ptSnapOut->s16ImgW);

    //编码存储
    if(NVR_ERR__OK != NvrFixCoiSaveJpeg(tPicPathParam.achPicPath, pPicAddr, ptSnapOut->u32JpgBytes))
    {
        PRINTERR("NvrFixCoiSaveJpeg Failed !!!\r\n");
        pthread_mutex_unlock(&(g_tEventCoiHandle.nPicLock));
        return;
    }

    PRINTVIP("pic path:%s, bytes:%d\n", tPicPathParam.achPicPath, ptSnapOut->u32JpgBytes);
    g_pPicAddr = pPicAddr;

    //图片存储锁
    pthread_mutex_unlock(&(g_tEventCoiHandle.nPicLock));

    if (ptAlarmEventInfo->dwPostType & 0x1)
    {
        NvrFixEventDealForCoi(ptAlarmEventInfo, pPicAddr, ptSnapOut, &tEventSysTime, &tPicPathParam);
    }

    if (ptAlarmEventInfo->dwPostType & 0x2) 
    {
        NvrFixEventDealForPubsec(ptAlarmEventInfo, pPicAddr, ptSnapOut, &tEventSysTime, &tPicPathParam);
    }

    g_tEventCoiHandle.dwCurPicNum++;
    return;
}


//事件告警信息队列处理线程
static void* NvrFixReportCoiEventThread(void)
{
    TThreadInfoRecord tThreadInfo;
    TAlarmEventInfo tEventInfoParam;
    TNvrDoubleListPopAttr tPopAttr;
    u8 byTargetIdx;
    static TNvrCapFixInernalCapInfo tInterCapParam;
    static TNvrCapLcam tCapLcam;
    BOOL bEventDeal = FALSE;

    mzero(tThreadInfo);
    mzero(tEventInfoParam);
    mzero(tPopAttr);
    mzero(tInterCapParam);
    mzero(tCapLcam);

    //添加线程信息
    tThreadInfo.m_dwThreadId = getpid();
    mcopy(tThreadInfo.m_strThreadName, "SnapPicThrd"); 
    OsApi_AddThreadInfo(&tThreadInfo);

    PRINTERR("Thread %d created!\n", getpid());

    NvrCapGetFixInterCapParam(&tInterCapParam);

    NvrCapGetCapParam(NVR_CAP_ID_LCAM, &tCapLcam);

    u32 u32JpgBufLen = tCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wWidth * tCapLcam.tLcamMcInfo.tCapture.tMaxResParam.wHeight;

#ifndef WIN32
    prctl(PR_SET_NAME, "SnapPicThrd", 0, 0, 0);
#endif

    //线程绑核
#if (defined _HIS3559A_)
    cpu_set_t tCpuMask;
    CPU_ZERO(&tCpuMask);
    CPU_SET(2, &tCpuMask);
    CPU_SET(3, &tCpuMask);
    if (-1 == sched_setaffinity(0, sizeof(cpu_set_t), &tCpuMask))
    {
        PRINTERR("sched_setaffinity Failed !!!\r\n");
    }
#endif

    //出栈参数
    tPopAttr.byBlockMode = NVR_QUEUE_POP_BLOCK;
    tPopAttr.dwDataLen = sizeof(TAlarmEventInfo);
    tPopAttr.pchDataBuf = (s8 *)&tEventInfoParam;

    while (TRUE)
    {
        //告警事件信息数据出栈，阻塞模式。等待告警事件信息出栈
        if (NVR_ERR__OK == NvrQueuePop(g_tEventCoiHandle.ptEventInfoQueue, &tPopAttr))
        {
            g_tEventCoiDbgInfo.dwAlarmInfoPopNum++;
            PRINTERR("NvrQueuePop succ !!!\r\n");
        }
        else
        {
            g_tEventCoiDbgInfo.dwAlarmInfoPopFailedNum++;
            PRINTERR("NvrQueuePop Failed !!!\r\n");
            continue;
        }

        //事件告警COI上报处理
        g_tEventCoiDbgInfo.dwCoiSupportNum++;

        TBasicIntelliSnapInput tSnapInput;
        TBasicIntelliSnapOutput tSnapOutput;
        if(tInterCapParam.eLcamChnType[tEventInfoParam.nCapChn] == NVR_CAP_CHNTYPE_VISIBLE)  //可见光通道
        {
            tSnapInput.u8SnapMethod = 1;  //可见光使用采集帧
            if(tInterCapParam.abyFixBasicIntelliSnapMethod[tEventInfoParam.nCapChn])
                tSnapInput.u8SnapMethod = tInterCapParam.abyFixBasicIntelliSnapMethod[tEventInfoParam.nCapChn];  //另外指定了抓拍方式
        }
        else if(tInterCapParam.eLcamChnType[tEventInfoParam.nCapChn] == NVR_CAP_CHNTYPE_THERMAL)//热成像通道
        {
            tSnapInput.u8SnapMethod = 2;  //热成像使用编码帧
            tSnapInput.u8LcamVidEncId = 0;  //抓拍主流
        }
        else
        {
            tSnapInput.u8SnapMethod = 0;  //自动
        }
        tSnapInput.u8LcamChnId = tEventInfoParam.nCapChn;

        tSnapInput.u32JpgBufLen = u32JpgBufLen;
        tSnapInput.pu8JpgBuf = (u8 *)NVRALLOC(tSnapInput.u32JpgBufLen);
        if(NULL == tSnapInput.pu8JpgBuf)         
        {
            PRINTERR("malloc size=%d failed\n", tSnapInput.u32JpgBufLen);
            continue;
        }

        for(byTargetIdx = 0; byTargetIdx < tEventInfoParam.wTargetNum; byTargetIdx++)
        {
            tSnapInput.tTargets[byTargetIdx] = tEventInfoParam.atAbnormTargets[byTargetIdx];
        }
        tSnapInput.u8DrawAlarmRect = tEventInfoParam.wTargetNum;
		
        tSnapInput.eEventType = tEventInfoParam.eEventType;
		if (NVR_ALARM_TYPE_SMART == tEventInfoParam.eEventType)
		{
			tSnapInput.eSnapAlarmType = tEventInfoParam.eAlmSDType;
			tSnapInput.tRegion = tEventInfoParam.tRegion;
			memcpy(tSnapInput.atNvrIntelCordon,tEventInfoParam.atNvrIntelCordon,NVR_MAX_CORDON_NUM* sizeof(TNvrIntelCordon));
		}
		
        tSnapInput.s64TimeStamp = tEventInfoParam.s64TimeStamp;
        //basicintelli内部抓拍接口 BasicIntelliSnapshot(TBasicIntelliSnapInput *ptInput, TBasicIntelliSnapOutput *ptOutput); 
#ifdef _BASIC_INTELLI_
        if (NVR_ERR__OK == NvrFixProfeIntelSnapshot(&tSnapInput, &tSnapOutput))
        {
            g_tEventCoiDbgInfo.dwPicSnapNum++;
            bEventDeal = TRUE;
        }
        else
        {
            g_tEventCoiDbgInfo.dwPicSnapFailedNum++;
            bEventDeal = FALSE;
            PRINTERR("BasicIntelliSnapshot Failed !!!\r\n");
        }
#else
        mzero(tSnapOutput);
#endif
        if (bEventDeal)
        {
            NvrFixReportEventDeal(&tEventInfoParam, tSnapInput.pu8JpgBuf, &tSnapOutput);
        }

        NVRFREE(tSnapInput.pu8JpgBuf);
    }

    PRINTERR("Task Quit...\n");

    OsApi_DelThreadInfo(tThreadInfo.m_dwThreadId);
    OsApi_TaskExit();
    return NULL;
}

//添加事件告警COI上报队列
NVRSTATUS NvrFixEventNotify(TAlarmEventInfo *ptEventInfoCbData)
{
    TNvrDoubleListPushAttr tPushAttr;

    mzero(tPushAttr);

    //入参检查
    if (NULL == g_tEventCoiHandle.ptEventInfoQueue || NULL == ptEventInfoCbData)
    {
        PRINTERR("g_tItEventInfoHandle.ptEventInfoQueue = NULL or ptEventInfoCbData = NULL !!!\r\n");
        return NVR_ERR__ERROR;
    }

    //进栈参数
    tPushAttr.dwDataLen = sizeof(TAlarmEventInfo);
    tPushAttr.pchDataBuf = (s8 *)ptEventInfoCbData;

    //告警信息数据进栈
    if (NVR_ERR__OK == NvrQueuePush(g_tEventCoiHandle.ptEventInfoQueue, &tPushAttr))
    {
        g_tEventCoiDbgInfo.dwAlarmInfoPushNum++;
    }
    else
    {
        g_tEventCoiDbgInfo.dwAlarmInfoPushFailedNum++;
        PRINTERR("NvrQueuePush Failed !!!\r\n");
        return NVR_ERR__ERROR;
    }

    return NVR_ERR__OK;

}

//定期删除图片
static s32 NvrFixDeleteCoiPicTimerCB()
{
    //文件删除参数
    s32 s32Index;
    u64 dwCurTime;

    //文件删除，超过20s  
    dwCurTime = NvrSysGetCurTimeMSec();

    pthread_mutex_lock(&(g_tEventCoiHandle.nPicLock));
    for(s32Index = 0; s32Index < g_tEventCoiHandle.dwCurPicNum; s32Index++)//遍历
    {   
        if(dwCurTime - g_tEventCoiHandle.tEventPicPathParam[s32Index].dwEventTime >= 20 * 1000
            && g_tEventCoiHandle.tEventPicPathParam[s32Index].dwEventTime != 0)//检查是否有文件时间超20s
        {
            if(0 == access(g_tEventCoiHandle.tEventPicPathParam[s32Index].achPicPath, F_OK))//文件存在，进行删除操作
            {
                if(0 == remove(g_tEventCoiHandle.tEventPicPathParam[s32Index].achPicPath))//删除成功，则进行数组操作
                {
                    g_tEventCoiDbgInfo.dwPicDelTotalNum++;
                    g_tEventCoiHandle.tEventPicPathParam[s32Index] = g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum - 1];
                    mzero(g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum - 1]);
                    g_tEventCoiHandle.dwCurPicNum--;
                    s32Index--;
                    continue;
                }
                else
                {
                    g_tEventCoiDbgInfo.dwPicDelFailedTotalNum++;
                    PRINTERR("Picture %s Removed Failed !!!\r\n", g_tEventCoiHandle.tEventPicPathParam[s32Index].achPicPath);
                }
            }
            else//文件不存在，直接进行数组操作
            {
                PRINTERR("Picture Not Exist !!!\r\n");
                g_tEventCoiDbgInfo.dwPicNotExistNum++;
                g_tEventCoiHandle.tEventPicPathParam[s32Index] = g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum-1];
                mzero(g_tEventCoiHandle.tEventPicPathParam[g_tEventCoiHandle.dwCurPicNum - 1]);
                g_tEventCoiHandle.dwCurPicNum--;
                s32Index--;
                continue;
            }
            
        }
    }
    pthread_mutex_unlock(&(g_tEventCoiHandle.nPicLock));

    OsApi_TimerSet(g_tEventCoiHandle.hDelPicTimer, 100, NvrFixDeleteCoiPicTimerCB, NULL);
    
    return 0;
}

static NVRSTATUS NvrFixCoreCoiInfoShow()
{
    //图片存储统计
    PRINTVIP("--About Picture:\n");
    PRINTVIP("------dwPicWriteSucNum:  %d\n", g_tEventCoiDbgInfo.dwPicWriteSucNum);
    PRINTVIP("------dwCurPicNum:        %d\n", g_tEventCoiHandle.dwCurPicNum);
    PRINTVIP("------dwPicDelTotalNum:   %d\n", g_tEventCoiDbgInfo.dwPicDelTotalNum);
    PRINTVIP("------dwPicDelFailedTotalNum: %d\n", g_tEventCoiDbgInfo.dwPicDelFailedTotalNum);
    PRINTVIP("------dwPicWriteFailNum:  %d\n", g_tEventCoiDbgInfo.dwPicWriteFailNum);
    PRINTVIP("------dwPicNotExistNum:       %d\n", g_tEventCoiDbgInfo.dwPicNotExistNum);

    //告警事件信息进栈统计
    PRINTVIP("--About AlarmInfoPush:\n");
    PRINTVIP("------dwAlarmInfoPushNum:         %u\n", g_tEventCoiDbgInfo.dwAlarmInfoPushNum);
    PRINTVIP("------dwAlarmInfoPushFailedNum:   %u\n", g_tEventCoiDbgInfo.dwAlarmInfoPushFailedNum);
    PRINTVIP("------dwAlarmInfoPopNum:          %u\n", g_tEventCoiDbgInfo.dwAlarmInfoPopNum);
    PRINTVIP("------dwAlarmInfoPopFailedNum:    %u\n", g_tEventCoiDbgInfo.dwAlarmInfoPopFailedNum);

    //COI支持统计
    PRINTVIP("--About CoiSupport:\n");
    PRINTVIP("------dwCoiSupportNum:         %u\n", g_tEventCoiDbgInfo.dwCoiSupportNum);
    PRINTVIP("------dwCoiSupportFailedNum:   %u\n", g_tEventCoiDbgInfo.dwCoiSupportFailedNum);


    //图片抓拍统计
    PRINTVIP("--About PicSnap:\n");
    PRINTVIP("------dwPicSnapNum:           %u\n", g_tEventCoiDbgInfo.dwPicSnapNum);
    PRINTVIP("------dwPicSnapFailedNum:     %u\n", g_tEventCoiDbgInfo.dwPicSnapFailedNum);

    //图片打开统计
    PRINTVIP("--About PicPathOpen:\n");
    PRINTVIP("------dwPicPathOpenNum:          %u\n", g_tEventCoiDbgInfo.dwPicPathOpenNum);
    PRINTVIP("------dwPicPathOpenFailedNum:    %u\n", g_tEventCoiDbgInfo.dwPicPathOpenFailedNum);

    //COI上报统计
    PRINTVIP("--About CoiReport:\n");
    PRINTVIP("------dwCoiReportNum:         %u\n", g_tEventCoiDbgInfo.dwCoiReportNum);
    PRINTVIP("------dwCoiReportFailedNum:   %u\n", g_tEventCoiDbgInfo.dwCoiReportFailedNum);

    //告警事件信息统计
    PRINTVIP("--About AlarmAbnorm:\n");
    PRINTVIP("------dwObjTypeNormalNum:       %u\n", g_tEventCoiDbgInfo.dwObjTypeNormalNum);
    PRINTVIP("------dwObjTypeAbnormNum:       %u\n", g_tEventCoiDbgInfo.dwObjTypeAbnormNum);
    PRINTVIP("------dwMotionEventNum:         %u\n", g_tEventCoiDbgInfo.dwMotionEventNum);

    //其他信息统计
    PRINTVIP("--About Others:\n");
    //抓拍回调上下文判断异常
    PRINTVIP("------dwContextErrNum:         %u\n", g_tEventCoiDbgInfo.dwContextErrNum);
    //抓拍索引号判断异常
    PRINTVIP("------dwSnapIndexErrNum:         %u\n", g_tEventCoiDbgInfo.dwSnapIndexErrNum);

    return NVR_ERR__OK;
}


NVRSTATUS NvrFixReportCoiInit(void)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;

    //初始化图片存储锁
    pthread_mutex_init(&g_tEventCoiHandle.nPicLock, NULL);

    //创建事件告警信息队列
    if (NVR_ERR__OK != NvrQueueCreate(&g_tEventCoiHandle.ptEventInfoQueue))
    {
        PRINTERR("NvrQueueCreate Failed !!!\r\n");
        return NVR_ERR__ERROR;
    }

    //创建线程
    g_tEventCoiHandle.u32EventInfoQueueTask = OsApi_TaskCreate((LINUXFUNC)NvrFixReportCoiEventThread, "_SnapAlarmPicThread", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL);
    if ((TASKHANDLE)NULL == g_tEventCoiHandle.u32EventInfoQueueTask)
    {
        PRINTERR("OsApi_TaskCreate NvrFixReportCoiEventThread Failed !!!\r\n");
        return NVR_ERR__ERROR;
    }

    //创建定时器
    g_tEventCoiHandle.hDelPicTimer = NULL;
    OsApi_TimerNew(&g_tEventCoiHandle.hDelPicTimer);
    if(g_tEventCoiHandle.hDelPicTimer)
        OsApi_TimerSet(g_tEventCoiHandle.hDelPicTimer, 100, NvrFixDeleteCoiPicTimerCB, NULL);
    else
        PRINTERR("OsApi_TimerNew hDelPicTimer Failed !!!\r\n");

    //调试命令
    OsApi_RegCommand( "coidbginfo", (void *)NvrFixCoreCoiInfoShow, "Print Alarm Coi Info" );
    //OsApi_RegCommand( "alarmdebug", (void *)_ALarmDebug, "_ALarmDebug：Abnorm Target Analog" );

    return eRet;
}


void NvrFixCoreGetAlarmLinkParamTest(u8 wDevId, u8 byAlarmType, u8 byAlarmNum)
{
    TNvrAlarmSrc tAlarmSrc;
    TNvrAlarmLinkPattern tLinkPattern;
    mzero(tAlarmSrc);
    mzero(tLinkPattern);
    tAlarmSrc.wDevId = wDevId;
    tAlarmSrc.byAlarmType = byAlarmType;
    tAlarmSrc.byAlarmNum = byAlarmNum;
    NvrAlarmGetAlarmLinkPattern(tAlarmSrc, &tLinkPattern);
    PRINTERR("wDevId:%u, byAlarmType:%u, byAlarmNum:%u  byPostCoi: %u\n", wDevId, byAlarmType, byAlarmNum, tLinkPattern.tRegularLink.byPostCoi);
    if(1)
    {
        TAlarmEventInfo tEventInfoCbData;
        mzero(tEventInfoCbData);
        tEventInfoCbData.nCapChn = wDevId;
        tEventInfoCbData.eEventType = byAlarmType;
        tEventInfoCbData.eAlmSDType = byAlarmNum;
        tEventInfoCbData.wTargetNum = 1;
        tEventInfoCbData.atAbnormTargets[0].eObjType = MEDIACTRL_OBJTYPE_PED;
        NvrFixEventNotify(&tEventInfoCbData);
    }
}

void NvrFIxCoreIpChangeCB(const u32 dwEthId, const ENvrEthParamChangeType eChangeType, void *pCBParam)
{
    PRINTERR("ethid:%lu,changetype:%d\n",dwEthId,eChangeType);

    switch(eChangeType)
    {
        ///<ip变化通知，重新设置vlan ip
        case NVR_ETH_PARAM_DHCP_CHANGE:
        case NVR_ETH_PARAM_IP_CHANGE:
        {
            system("ifconfig eth0:0 ******** netmask *************");
        }
        break;
        default:
            break;
    }
}

APP_API BOOL32 SpecialFeatureStart(void)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;

    DebugLogRegist(DEBUG_LOG_MOD_FIX_CORE,"[FIXCORE]",NVR_SRV_DEBUGLOG_NAME);
    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, TRUE);

    mzero(g_tNvrFixInterCap);

    NvrCapGetFixInterCapParam(&g_tNvrFixInterCap);

	NvrFixCfgInit();

	NvrFixIspModuleInit();

	NvrFixMcModuleInit();

#if (defined _HIS3559A_)
	if( !OsApi_SemBCreate(&g_hFixSnapSem))
    {
        PRINTERR("create snap g_hFixSnapSem sem failed\n");
		return FALSE;
    }
		
	NvrMpuSpecialDataCallBack((TNvrMpuSpecialDataCB)NvrOnvifProSnapFixCB,NULL);
#else
    NvrFixInitProbe();

    NvrFixInitInfrared();

    NvrFixInitLdr();

    NvrFixUserSafeInit();

	
	if( !OsApi_SemBCreate(&g_hFixSnapSem))
	{
		PRINTERR("create snap g_hFixSnapSem sem failed\n");
		return FALSE;
	}

	NvrMpuSpecialDataCallBack((TNvrMpuSpecialDataCB)NvrMultiSrcProSnapFixCB,NULL);

	AirpSetMultiSnapCallBack(NvrAirpMultiSnapFixCB);
	
#endif

    NvrFixDevModuleInit();

    LcamSetSpecialCtrlCB(NvrFixLcamMessageCb,NULL);

    NvrSysRecoveryRegistCB(NvrFixCoreSysRecovCB);

	LcamSetSpecialEventCB(NvrFixLcamMessageEventCb,NULL);

	NvrFixCheckTaskInit();

	NvrFixPluginInit();

	NvrFixLifeStatTaskInit();

#ifdef _SSC339G_
    // 初始化NvrFixCoi模块，用于处理服务端TRANSDATA消息
    NvrFixCoiInit();
#endif

    NvrFixReportCoiInit();

    //注册回调通知上联平台状态变化，确保只有一个地方调用
    NvrSysNotifyPlatSetCallBack(NvrFixSysNotifyPlatState);

	///<2241从机升级回调
	NvrSysSlaveUpgradeRegistCB(NvrFixSetXs5012UpdateCb);
	NvrSysSlaveUpgradeStateRegistCB(NvrFixSlaveUpgradeStateCb);
	NvrSysSlaveUpgradeNeedRegistCB(NvrFixSlaveNeedUpgradeCb);

	
#ifdef _BASIC_INTELLI_      ///< NvrFixProfeIntelxxx接口需要编译时增加 nvrfixprofeintelli文件，目前只添加cv2x，后续各平台可相应添加
	if (g_tNvrFixInterCap.tFixProIntelCap.bySupAdvBasicintel)
	{
	    eRet = NvrFixProfeIntelInit();
	    NvrFixLog("NvrFixProfeIntelInit %s \n", NVR_ERR__OK == eRet ? "succ" : "fail");
	}

    #ifdef _SSC339G_
    AisUpdExSetResultCallBack(AIS_DATA_UPLOAD_TYPE_PUBSEC1, AIS_UPLOAD_DATA_PIC_FACE_SNAP, NvfFixPubResultAchieve, NULL);
    #endif
#endif

	NvrFixXs5012NeedUpgrade();
	NvrFixXs5012UpdateTask();
	NvrFixGetXs5012UpdateFlag();

	
	MAKE_COMPILER_HAPPY(sASF_ErrorInfo);
	MAKE_COMPILER_HAPPY(JdgAudio);
	MAKE_COMPILER_HAPPY(GetSampleRate);
	MAKE_COMPILER_HAPPY(GetAudFormatIdx);
	MAKE_COMPILER_HAPPY(GetAudioEncTypeArrIdx);
	MAKE_COMPILER_HAPPY(UmpContextToAudioMode);
    MAKE_COMPILER_HAPPY(sample_rate_idx);

    ///<调试命令注册
    OsApi_RegCommand( "ipcver", (void *)NvrFixIpcVer, "show ipc ver" );
    OsApi_RegCommand( "openssh2", (void *)NvrFixOpenssh, "openssh2" );
    OsApi_RegCommand( "fixptzver", (void *)NvrFixPtzVer, "fixptzver" );    
    OsApi_RegCommand( "fixptz", (void *)NvrFixDevPtzTest, "fixptz" );
    OsApi_RegCommand( "getldr", (void *)NvrFixGetIdr, "getldr" );			//红外抢生产使用
    OsApi_RegCommand( "getthr", (void *)NvrFixGetDnThreshold, "getthr");
	OsApi_RegCommand( "setthr", (void *)NvrFixSetDnThreshold, "setthr");
    OsApi_RegCommand( "switchircut", (void*)NvrFixSwitchIrcut, "Switch IRCUT");
    OsApi_RegCommand( "multiserialtest", (void*)NvrFixMultiSerialTest, "multi serial test");
    OsApi_RegCommand( "cleare2prominfo", (void *)NvrFixClearE2promInfo, "cleare2prominfo");
    OsApi_RegCommand( "settestflag", (void *)NvrFixSetTestFlag, "settestflag");
    OsApi_RegCommand( "ispshow", (void*)NvrFixShowIspInfo, "show isp info on osd" );
    OsApi_RegCommand( "coitest", NvrFixCoreGetAlarmLinkParamTest, "show alarm link coi" );
    OsApi_RegCommand( "firmwareupdate", (void*)NvrFixFirmwareUpdate, "firmwareupdate");
	OsApi_RegCommand( "serialechotest", (void*)NvrFixSerialEchoTest, "serial echo test");
	OsApi_RegCommand( "xsupload", (void*)NvrFixSetXs5012Update, "xs5012 upload");
	OsApi_RegCommand( "xs5012ver", (void*)NvrFixXs5012Version, "xs5012 version");

    DebugLogMoudlePrintLevelOn(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, FALSE);

	return TRUE;
}

